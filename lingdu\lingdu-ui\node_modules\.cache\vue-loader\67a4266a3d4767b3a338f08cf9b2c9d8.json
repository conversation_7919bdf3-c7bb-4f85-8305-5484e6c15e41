{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\register.vue?vue&type=style&index=0&id=77453986&rel=stylesheet%2Fscss&lang=scss&scoped=true", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\register.vue", "mtime": 1758270624441}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758264042901}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758264044469}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758264043503}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758264042403}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758264043933}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoucmVnaXN0ZXIgewogIG1pbi1oZWlnaHQ6IDEwMHZoOwogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIHBhZGRpbmc6IDIwcHg7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIG92ZXJmbG93OiBoaWRkZW47CgogICY6OmJlZm9yZSB7CiAgICBjb250ZW50OiAnJzsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIHRvcDogMDsKICAgIGxlZnQ6IDA7CiAgICByaWdodDogMDsKICAgIGJvdHRvbTogMDsKICAgIGJhY2tncm91bmQ6IHVybCgiLi4vYXNzZXRzL2ltYWdlcy9sb2dpbi1iYWNrZ3JvdW5kLmpwZyIpIGNlbnRlci9jb3ZlcjsKICAgIG9wYWNpdHk6IDAuMTsKICAgIHotaW5kZXg6IDA7CiAgfQoKICAvLyDmt7vliqDliqjmgIHog4zmma/nspLlrZDmlYjmnpwKICAmOjphZnRlciB7CiAgICBjb250ZW50OiAnJzsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIHRvcDogMDsKICAgIGxlZnQ6IDA7CiAgICByaWdodDogMDsKICAgIGJvdHRvbTogMDsKICAgIGJhY2tncm91bmQtaW1hZ2U6IAogICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDIwJSA4MCUsIHJnYmEoMTIwLCAxMTksIDE5OCwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSwKICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA4MCUgMjAlLCByZ2JhKDI1NSwgMTE5LCAxOTgsIDAuMykgMCUsIHRyYW5zcGFyZW50IDUwJSksCiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgNDAlIDQwJSwgcmdiYSgxMjAsIDIxOSwgMjU1LCAwLjMpIDAlLCB0cmFuc3BhcmVudCA1MCUpOwogICAgYW5pbWF0aW9uOiBiYWNrZ3JvdW5kU2hpZnQgOHMgZWFzZS1pbi1vdXQgaW5maW5pdGU7CiAgICB6LWluZGV4OiAwOwogIH0KCiAgLnJlZ2lzdGVyLWNvbnRhaW5lciB7CiAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICB6LWluZGV4OiAxOwogICAgd2lkdGg6IDEwMCU7CiAgICBtYXgtd2lkdGg6IDYwMHB4OwogIH0KCgoKICAucmVnaXN0ZXItY2FyZCB7CiAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOTUpOwogICAgYm9yZGVyLXJhZGl1czogMjRweDsKICAgIHBhZGRpbmc6IDQ1cHg7CiAgICBib3gtc2hhZG93OiAKICAgICAgMCAyNXB4IDUwcHggcmdiYSgwLCAwLCAwLCAwLjE1KSwKICAgICAgMCAxMHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjEpLAogICAgICBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC42KTsKICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxNXB4KTsKICAgIHRyYW5zaXRpb246IGFsbCAwLjRzIGN1YmljLWJlemllcigwLjE3NSwgMC44ODUsIDAuMzIsIDEuMjc1KTsKICAgIGFuaW1hdGlvbjogc2xpZGVJblVwIDAuOHMgZWFzZS1vdXQ7CiAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpOwoKICAgIC8vIOWFieazveaViOaenAogICAgJjo6YmVmb3JlIHsKICAgICAgY29udGVudDogJyc7CiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgdG9wOiAtNTAlOwogICAgICBsZWZ0OiAtNTAlOwogICAgICB3aWR0aDogMjAwJTsKICAgICAgaGVpZ2h0OiAyMDAlOwogICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoNDVkZWcsIHRyYW5zcGFyZW50LCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSksIHRyYW5zcGFyZW50KTsKICAgICAgdHJhbnNmb3JtOiByb3RhdGUoNDVkZWcpOwogICAgICB0cmFuc2l0aW9uOiBhbGwgMC42cyBlYXNlOwogICAgICBvcGFjaXR5OiAwOwogICAgICBwb2ludGVyLWV2ZW50czogbm9uZTsKICAgICAgei1pbmRleDogMTsKICAgIH0KCiAgICAvLyDovrnmoYblhYnmlYgKICAgICY6OmFmdGVyIHsKICAgICAgY29udGVudDogJyc7CiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgdG9wOiAwOwogICAgICBsZWZ0OiAwOwogICAgICByaWdodDogMDsKICAgICAgYm90dG9tOiAwOwogICAgICBib3JkZXItcmFkaXVzOiAyNHB4OwogICAgICBwYWRkaW5nOiAycHg7CiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg0NWRlZywgdHJhbnNwYXJlbnQsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKSwgdHJhbnNwYXJlbnQpOwogICAgICBtYXNrOiBsaW5lYXItZ3JhZGllbnQoI2ZmZiAwIDApIGNvbnRlbnQtYm94LCBsaW5lYXItZ3JhZGllbnQoI2ZmZiAwIDApOwogICAgICBtYXNrLWNvbXBvc2l0ZTogZXhjbHVkZTsKICAgICAgb3BhY2l0eTogMDsKICAgICAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjNzIGVhc2U7CiAgICAgIHBvaW50ZXItZXZlbnRzOiBub25lOwogICAgICB6LWluZGV4OiAxOwogICAgfQoKICAgICY6aG92ZXIgewogICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLThweCkgc2NhbGUoMS4wMik7CiAgICAgIGJveC1zaGFkb3c6IAogICAgICAgIDAgMzVweCA3MHB4IHJnYmEoMCwgMCwgMCwgMC4yNSksCiAgICAgICAgMCAxNXB4IDMwcHggcmdiYSgwLCAwLCAwLCAwLjE1KSwKICAgICAgICBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTsKCiAgICAgICY6OmJlZm9yZSB7CiAgICAgICAgb3BhY2l0eTogMTsKICAgICAgICB0cmFuc2Zvcm06IHJvdGF0ZSg0NWRlZykgdHJhbnNsYXRlKDUwJSwgNTAlKTsKICAgICAgfQoKICAgICAgJjo6YWZ0ZXIgewogICAgICAgIG9wYWNpdHk6IDE7CiAgICAgIH0KICAgIH0KCiAgICAuY2FyZC1oZWFkZXIgewogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgIG1hcmdpbi1ib3R0b206IDMwcHg7CgogICAgICAuaWRlbnRpdHktaWNvbiB7CiAgICAgICAgd2lkdGg6IDgwcHg7CiAgICAgICAgaGVpZ2h0OiA4MHB4OwogICAgICAgIG1hcmdpbjogMCBhdXRvIDIwcHg7CiAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlOwogICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwoKICAgICAgICAuaWNvbiB7CiAgICAgICAgICBmb250LXNpemU6IDIuNXJlbTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5jYXJkLXRpdGxlIHsKICAgICAgICBmb250LXNpemU6IDEuOHJlbTsKICAgICAgICBmb250LXdlaWdodDogNjAwOwogICAgICAgIG1hcmdpbjogMCAwIDEwcHggMDsKICAgICAgICBjb2xvcjogIzMzMzsKICAgICAgfQoKICAgICAgLmNhcmQtc3VidGl0bGUgewogICAgICAgIGZvbnQtc2l6ZTogMXJlbTsKICAgICAgICBjb2xvcjogIzY2NjsKICAgICAgICBtYXJnaW46IDA7CiAgICAgIH0KICAgIH0KCiAgICAuaWRlbnRpdHktc3dpdGNoIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgYmFja2dyb3VuZDogcmdiYSgyNDUsIDI0NSwgMjQ1LCAwLjgpOwogICAgICBib3JkZXItcmFkaXVzOiAxNnB4OwogICAgICBwYWRkaW5nOiA2cHg7CiAgICAgIG1hcmdpbi1ib3R0b206IDM1cHg7CiAgICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cig1cHgpOwogICAgICBib3gtc2hhZG93OiBpbnNldCAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjA1KTsKCiAgICAgIC5zd2l0Y2gtaXRlbSB7CiAgICAgICAgZmxleDogMTsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgcGFkZGluZzogMTRweCAxMHB4OwogICAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7CiAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjRzIGN1YmljLWJlemllcigwLjE3NSwgMC44ODUsIDAuMzIsIDEuMjc1KTsKICAgICAgICBmb250LXNpemU6IDE1cHg7CiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsKICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKCiAgICAgICAgJjo6YmVmb3JlIHsKICAgICAgICAgIGNvbnRlbnQ6ICcnOwogICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICAgICAgdG9wOiAwOwogICAgICAgICAgbGVmdDogMDsKICAgICAgICAgIHJpZ2h0OiAwOwogICAgICAgICAgYm90dG9tOiAwOwogICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNikpOwogICAgICAgICAgYm9yZGVyLXJhZGl1czogMTJweDsKICAgICAgICAgIG9wYWNpdHk6IDA7CiAgICAgICAgICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuM3MgZWFzZTsKICAgICAgICAgIHBvaW50ZXItZXZlbnRzOiBub25lOwogICAgICAgIH0KCiAgICAgICAgLnN3aXRjaC1pY29uIHsKICAgICAgICAgIG1hcmdpbi1yaWdodDogOHB4OwogICAgICAgICAgZm9udC1zaXplOiAxOHB4OwogICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKICAgICAgICB9CgogICAgICAgICYuYWN0aXZlIHsKICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICAgICAgICBjb2xvcjogIzQwOUVGRjsKICAgICAgICAgIGJveC1zaGFkb3c6IAogICAgICAgICAgICAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSksCiAgICAgICAgICAgIDAgMnB4IDRweCByZ2JhKDY0LCAxNTgsIDI1NSwgMC4yKTsKICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsKCiAgICAgICAgICAmOjpiZWZvcmUgewogICAgICAgICAgICBvcGFjaXR5OiAxOwogICAgICAgICAgfQoKICAgICAgICAgIC5zd2l0Y2gtaWNvbiB7CiAgICAgICAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4xKTsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgICY6aG92ZXI6bm90KC5hY3RpdmUpIHsKICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsKICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsKICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgICAgICAgfQogICAgICB9CiAgICB9CgogICAgLnJlZ2lzdGVyLWZvcm0gewogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgIHotaW5kZXg6IDI7CiAgICAgIAogICAgICAuZm9ybS1zZWN0aW9uIHsKICAgICAgICBtYXJnaW4tYm90dG9tOiAzMHB4OwoKICAgICAgICAuc2VjdGlvbi10aXRsZSB7CiAgICAgICAgICBmb250LXNpemU6IDEuMXJlbTsKICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7CiAgICAgICAgICBjb2xvcjogIzMzMzsKICAgICAgICAgIG1hcmdpbjogMCAwIDIwcHggMDsKICAgICAgICAgIHBhZGRpbmctYm90dG9tOiAxMHB4OwogICAgICAgICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICNmMGYwZjA7CiAgICAgICAgfQoKICAgICAgICAuZm9ybS1yb3cgewogICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgIGdhcDogMTVweDsKICAgICAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CgogICAgICAgICAgLmZvcm0taXRlbS1oYWxmIHsKICAgICAgICAgICAgZmxleDogMTsKICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMDsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC5pbnB1dC1ncm91cCB7CiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAyNXB4OwoKICAgICAgICAgIC5pbnB1dC1pY29uIHsKICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICAgICAgICBsZWZ0OiAyMHB4OwogICAgICAgICAgICB0b3A6IDUwJTsKICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpOwogICAgICAgICAgICBjb2xvcjogIzk5OTsKICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICAgICAgICB6LWluZGV4OiAzOwogICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwogICAgICAgICAgfQoKICAgICAgICAgIC5jdXN0b20taW5wdXQgewogICAgICAgICAgICA6OnYtZGVlcCAuZWwtaW5wdXRfX2lubmVyIHsKICAgICAgICAgICAgICBoZWlnaHQ6IDU1cHggIWltcG9ydGFudDsKICAgICAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDYwcHggIWltcG9ydGFudDsKICAgICAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCAjZThlOGU4ICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMTZweCAhaW1wb3J0YW50OwogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweCAhaW1wb3J0YW50OwogICAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjRzIGN1YmljLWJlemllcigwLjE3NSwgMC44ODUsIDAuMzIsIDEuMjc1KSAhaW1wb3J0YW50OwogICAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KSAhaW1wb3J0YW50OwogICAgICAgICAgICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cig1cHgpOwogICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDUpICFpbXBvcnRhbnQ7CgogICAgICAgICAgICAgICY6Zm9jdXMgewogICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgICBib3gtc2hhZG93OiAKICAgICAgICAgICAgICAgICAgMCAwIDAgNHB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjE1KSwKICAgICAgICAgICAgICAgICAgMCA0cHggMTJweCByZ2JhKDY0LCAxNTgsIDI1NSwgMC4xKSAhaW1wb3J0YW50OwogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAxKSAhaW1wb3J0YW50OwogICAgICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2MwYzRjYyAhaW1wb3J0YW50OwogICAgICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4wOCkgIWltcG9ydGFudDsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KCiAgICAgICAgICAmOmhvdmVyIC5pbnB1dC1pY29uIHsKICAgICAgICAgICAgY29sb3I6ICM2NjY7CiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKSBzY2FsZSgxLjEpOwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLmN1c3RvbS1zZWxlY3QgewogICAgICAgICAgd2lkdGg6IDEwMCU7CgogICAgICAgICAgLmVsLWlucHV0X19pbm5lciB7CiAgICAgICAgICAgIGhlaWdodDogNTVweDsKICAgICAgICAgICAgYm9yZGVyOiAycHggc29saWQgI2U4ZThlODsKICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMTZweDsKICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC40cyBjdWJpYy1iZXppZXIoMC4xNzUsIDAuODg1LCAwLjMyLCAxLjI3NSk7CiAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsKICAgICAgICAgICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDVweCk7CiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDUpOwoKICAgICAgICAgICAgJjpmb2N1cyB7CiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGOwogICAgICAgICAgICAgIGJveC1zaGFkb3c6IAogICAgICAgICAgICAgICAgMCAwIDAgNHB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjE1KSwKICAgICAgICAgICAgICAgIDAgNHB4IDEycHggcmdiYSg2NCwgMTU4LCAyNTUsIDAuMSk7CiAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAxKTsKICAgICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgICY6aG92ZXIgewogICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2MwYzRjYzsKICAgICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7CiAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMDgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICAuY2FwdGNoYS1zZWN0aW9uIHsKICAgICAgICBtYXJnaW4tYm90dG9tOiAzMHB4OwoKICAgICAgICAuY2FwdGNoYS1ncm91cCB7CiAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICAgIGdhcDogMTVweDsKCiAgICAgICAgICAuY2FwdGNoYS1pbnB1dCB7CiAgICAgICAgICAgIGZsZXg6IDE7CiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDA7CiAgICAgICAgICB9CgogICAgICAgICAgLmNhcHRjaGEtaW1hZ2UgewogICAgICAgICAgICB3aWR0aDogMTIwcHg7CiAgICAgICAgICAgIGhlaWdodDogNTVweDsKICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMTZweDsKICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICNlOGU4ZTg7CiAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7CiAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsKICAgICAgICAgICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDVweCk7CiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDUpOwoKICAgICAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGOwogICAgICAgICAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wMik7CiAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMDgpOwogICAgICAgICAgICB9CgogICAgICAgICAgICAuY2FwdGNoYS1pbWcgewogICAgICAgICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgICAgICAgIGhlaWdodDogMTAwJTsKICAgICAgICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgLnJlZnJlc2gtdGlwIHsKICAgICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgICAgICAgICAgYm90dG9tOiAwOwogICAgICAgICAgICAgIGxlZnQ6IDA7CiAgICAgICAgICAgICAgcmlnaHQ6IDA7CiAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjcpOwogICAgICAgICAgICAgIGNvbG9yOiAjZmZmOwogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgICAgICAgICAgcGFkZGluZzogMnB4OwogICAgICAgICAgICAgIG9wYWNpdHk6IDA7CiAgICAgICAgICAgICAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjNzIGVhc2U7CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgICY6aG92ZXIgLnJlZnJlc2gtdGlwIHsKICAgICAgICAgICAgICBvcGFjaXR5OiAxOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICAucmVnaXN0ZXItYnRuLWl0ZW0gewogICAgICAgIG1hcmdpbi1ib3R0b206IDI1cHg7CgogICAgICAgIC5yZWdpc3Rlci1idG4gewogICAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgICBoZWlnaHQ6IDU1cHg7CiAgICAgICAgICBib3JkZXItcmFkaXVzOiAxNnB4OwogICAgICAgICAgZm9udC1zaXplOiAxN3B4OwogICAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDsKICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjRzIGN1YmljLWJlemllcigwLjE3NSwgMC44ODUsIDAuMzIsIDEuMjc1KTsKICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICAgICAgICBib3JkZXI6IG5vbmU7CiAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNDA5RUZGLCAjNjZiMWZmKTsKICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDE1cHggcmdiYSg2NCwgMTU4LCAyNTUsIDAuMyk7CgogICAgICAgICAgJjo6YmVmb3JlIHsKICAgICAgICAgICAgY29udGVudDogJyc7CiAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgICAgICAgdG9wOiAwOwogICAgICAgICAgICBsZWZ0OiAtMTAwJTsKICAgICAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgICAgIGhlaWdodDogMTAwJTsKICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpLCB0cmFuc3BhcmVudCk7CiAgICAgICAgICAgIHRyYW5zaXRpb246IGxlZnQgMC41cyBlYXNlOwogICAgICAgICAgfQoKICAgICAgICAgICY6aG92ZXIgewogICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTNweCkgc2NhbGUoMS4wMik7CiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMTJweCAzMHB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjQpOwogICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjZiMWZmLCAjNDA5RUZGKTsKCiAgICAgICAgICAgICY6OmJlZm9yZSB7CiAgICAgICAgICAgICAgbGVmdDogMTAwJTsKICAgICAgICAgICAgfQogICAgICAgICAgfQoKICAgICAgICAgICY6YWN0aXZlIHsKICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpIHNjYWxlKDAuOTgpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgLmxvZ2luLWxpbmsgewogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgICBjb2xvcjogIzY2NjsKICAgICAgICBmb250LXNpemU6IDE0cHg7CgogICAgICAgIC5saW5rIHsKICAgICAgICAgIGNvbG9yOiAjNDA5RUZGOwogICAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwogICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgICAgICAgIG1hcmdpbi1sZWZ0OiA1cHg7CiAgICAgICAgICB0cmFuc2l0aW9uOiBjb2xvciAwLjNzIGVhc2U7CgogICAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICAgIGNvbG9yOiAjNjZiMWZmOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQoKICAgIC8vIOS4u+mimOagt+W8jwogICAgJi5zdHVkZW50LXRoZW1lIHsKICAgICAgLmNhcmQtaGVhZGVyIC5pZGVudGl0eS1pY29uIHsKICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNDA5RUZGLCAjNjZiMWZmKTsKICAgICAgICBjb2xvcjogI2ZmZjsKICAgICAgfQogICAgfQoKICAgICYucGFyZW50LXRoZW1lIHsKICAgICAgLmNhcmQtaGVhZGVyIC5pZGVudGl0eS1pY29uIHsKICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjdDMjNBLCAjODVjZTYxKTsKICAgICAgICBjb2xvcjogI2ZmZjsKICAgICAgfQogICAgfQoKICAgICYudGVhY2hlci10aGVtZSB7CiAgICAgIC5jYXJkLWhlYWRlciAuaWRlbnRpdHktaWNvbiB7CiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI0U2QTIzQywgI2YwYzc4YSk7CiAgICAgICAgY29sb3I6ICNmZmY7CiAgICAgIH0KICAgIH0KICB9CgogIC5kZWNvcmF0aW9uIHsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIHRvcDogMDsKICAgIGxlZnQ6IDA7CiAgICByaWdodDogMDsKICAgIGJvdHRvbTogMDsKICAgIHBvaW50ZXItZXZlbnRzOiBub25lOwogICAgei1pbmRleDogMDsKCiAgICAuY2lyY2xlIHsKICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICBib3JkZXItcmFkaXVzOiA1MCU7CiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTsKICAgICAgYW5pbWF0aW9uOiBmbG9hdCA2cyBlYXNlLWluLW91dCBpbmZpbml0ZTsKCiAgICAgICYuY2lyY2xlLTEgewogICAgICAgIHdpZHRoOiAxMDBweDsKICAgICAgICBoZWlnaHQ6IDEwMHB4OwogICAgICAgIHRvcDogMjAlOwogICAgICAgIGxlZnQ6IDEwJTsKICAgICAgICBhbmltYXRpb24tZGVsYXk6IDBzOwogICAgICB9CgogICAgICAmLmNpcmNsZS0yIHsKICAgICAgICB3aWR0aDogMTUwcHg7CiAgICAgICAgaGVpZ2h0OiAxNTBweDsKICAgICAgICB0b3A6IDYwJTsKICAgICAgICByaWdodDogMTAlOwogICAgICAgIGFuaW1hdGlvbi1kZWxheTogMnM7CiAgICAgIH0KCiAgICAgICYuY2lyY2xlLTMgewogICAgICAgIHdpZHRoOiA4MHB4OwogICAgICAgIGhlaWdodDogODBweDsKICAgICAgICBib3R0b206IDIwJTsKICAgICAgICBsZWZ0OiAyMCU7CiAgICAgICAgYW5pbWF0aW9uLWRlbGF5OiA0czsKICAgICAgfQogICAgfQogIH0KfQoKLy8g5Yqo55S75a6a5LmJCkBrZXlmcmFtZXMgc2xpZGVJblVwIHsKICBmcm9tIHsKICAgIG9wYWNpdHk6IDA7CiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMzBweCk7CiAgfQogIHRvIHsKICAgIG9wYWNpdHk6IDE7CiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7CiAgfQp9CgpAa2V5ZnJhbWVzIGZsb2F0IHsKICAwJSwgMTAwJSB7CiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMHB4KTsKICB9CiAgNTAlIHsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMjBweCk7CiAgfQp9CgpAa2V5ZnJhbWVzIGJhY2tncm91bmRTaGlmdCB7CiAgMCUsIDEwMCUgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDApIHRyYW5zbGF0ZVkoMCk7CiAgfQogIDI1JSB7CiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTE1cHgpIHRyYW5zbGF0ZVkoLThweCk7CiAgfQogIDUwJSB7CiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMTVweCkgdHJhbnNsYXRlWSg4cHgpOwogIH0KICA3NSUgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC04cHgpIHRyYW5zbGF0ZVkoMTVweCk7CiAgfQp9CgpAa2V5ZnJhbWVzIHB1bHNlIHsKICAwJSwgMTAwJSB7CiAgICB0cmFuc2Zvcm06IHNjYWxlKDEpOwogIH0KICA1MCUgewogICAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTsKICB9Cn0KCkBrZXlmcmFtZXMgc2hpbW1lciB7CiAgMCUgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC0xMDAlKTsKICB9CiAgMTAwJSB7CiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMTAwJSk7CiAgfQp9CgovLyDlk43lupTlvI/orr7orqEKQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7CiAgLnJlZ2lzdGVyIHsKICAgIHBhZGRpbmc6IDEwcHg7CgogICAgLnJlZ2lzdGVyLWNvbnRhaW5lciB7CiAgICAgIG1heC13aWR0aDogMTAwJTsKICAgIH0KCiAgICAucmVnaXN0ZXItY2FyZCB7CiAgICAgIHBhZGRpbmc6IDMwcHggMjBweDsKICAgICAgbWFyZ2luLXRvcDogNDBweDsKCiAgICAgIC5jYXJkLWhlYWRlciAuaWRlbnRpdHktaWNvbiB7CiAgICAgICAgd2lkdGg6IDYwcHg7CiAgICAgICAgaGVpZ2h0OiA2MHB4OwoKICAgICAgICAuaWNvbiB7CiAgICAgICAgICBmb250LXNpemU6IDJyZW07CiAgICAgICAgfQogICAgICB9CgogICAgICAuaWRlbnRpdHktc3dpdGNoIHsKICAgICAgICAuc3dpdGNoLWl0ZW0gewogICAgICAgICAgcGFkZGluZzogMTBweCA0cHg7CiAgICAgICAgICBmb250LXNpemU6IDEycHg7CgogICAgICAgICAgLnN3aXRjaC1pY29uIHsKICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgLnJlZ2lzdGVyLWZvcm0gewogICAgICAgIC5mb3JtLXNlY3Rpb24gewogICAgICAgICAgLmZvcm0tcm93IHsKICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgICAgICAgICAgZ2FwOiAwOwoKICAgICAgICAgICAgLmZvcm0taXRlbS1oYWxmIHsKICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9CgoKICB9Cn0K"}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4a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file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"register\">\n    <div class=\"register-container\">\n\n\n      <!-- 注册卡片 -->\n      <div class=\"register-card\" :class=\"`${registerForm.userType}-theme`\">\n        <div class=\"card-header\">\n          <div class=\"identity-icon\">\n            <svg-icon :icon-class=\"getIdentityIcon()\" class=\"icon\" />\n          </div>\n          <h2 class=\"card-title\">{{ getIdentityTitle() }}</h2>\n          <p class=\"card-subtitle\">{{ getIdentitySubtitle() }}</p>\n        </div>\n\n        <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\n          <!-- 身份切换 -->\n          <div class=\"identity-switch\">\n            <div \n              v-for=\"type in identityTypes\" \n              :key=\"type.value\"\n              class=\"switch-item\"\n              :class=\"{ active: registerForm.userType === type.value }\"\n              @click=\"switchIdentity(type.value)\"\n            >\n              <svg-icon :icon-class=\"type.icon\" class=\"switch-icon\" />\n              <span>{{ type.label }}</span>\n            </div>\n          </div>\n\n          <!-- 基本信息 -->\n          <div class=\"form-section\">\n            <h4 class=\"section-title\">基本信息</h4>\n            \n            <div class=\"form-row\">\n              <el-form-item prop=\"username\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"user\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.username\"\n                    type=\"text\"\n                    auto-complete=\"off\"\n                    placeholder=\"请输入账号\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"phone\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"phone\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.phone\"\n                    type=\"text\"\n                    auto-complete=\"off\"\n                    placeholder=\"请输入手机号\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n\n            <el-form-item prop=\"realName\">\n              <div class=\"input-group\">\n                <svg-icon icon-class=\"user\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"registerForm.realName\"\n                  type=\"text\"\n                  auto-complete=\"off\"\n                  placeholder=\"请输入真实姓名\"\n                  class=\"custom-input\"\n                />\n              </div>\n            </el-form-item>\n\n            <div class=\"form-row\">\n              <el-form-item prop=\"password\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"password\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.password\"\n                    type=\"password\"\n                    auto-complete=\"off\"\n                    placeholder=\"请输入密码\"\n                    class=\"custom-input\"\n                    @keyup.enter.native=\"handleRegister\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"confirmPassword\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"password\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.confirmPassword\"\n                    type=\"password\"\n                    auto-complete=\"off\"\n                    placeholder=\"确认密码\"\n                    class=\"custom-input\"\n                    @keyup.enter.native=\"handleRegister\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 学生专用信息 -->\n          <div v-if=\"registerForm.userType === 'student'\" class=\"form-section\">\n            <h4 class=\"section-title\">学习信息</h4>\n            \n            <div class=\"form-row\">\n              <el-form-item prop=\"grade\" class=\"form-item-half\">\n                <el-select v-model=\"registerForm.grade\" placeholder=\"请选择年级\" class=\"custom-select\">\n                  <el-option label=\"小学一年级\" value=\"小学一年级\"></el-option>\n                  <el-option label=\"小学二年级\" value=\"小学二年级\"></el-option>\n                  <el-option label=\"小学三年级\" value=\"小学三年级\"></el-option>\n                  <el-option label=\"小学四年级\" value=\"小学四年级\"></el-option>\n                  <el-option label=\"小学五年级\" value=\"小学五年级\"></el-option>\n                  <el-option label=\"小学六年级\" value=\"小学六年级\"></el-option>\n                  <el-option label=\"初中一年级\" value=\"初中一年级\"></el-option>\n                  <el-option label=\"初中二年级\" value=\"初中二年级\"></el-option>\n                  <el-option label=\"初中三年级\" value=\"初中三年级\"></el-option>\n                  <el-option label=\"高中一年级\" value=\"高中一年级\"></el-option>\n                  <el-option label=\"高中二年级\" value=\"高中二年级\"></el-option>\n                  <el-option label=\"高中三年级\" value=\"高中三年级\"></el-option>\n                </el-select>\n              </el-form-item>\n\n              <el-form-item prop=\"stage\" class=\"form-item-half\">\n                <el-select v-model=\"registerForm.stage\" placeholder=\"请选择学段\" class=\"custom-select\">\n                  <el-option label=\"小学\" value=\"小学\"></el-option>\n                  <el-option label=\"初中\" value=\"初中\"></el-option>\n                  <el-option label=\"高中\" value=\"高中\"></el-option>\n                </el-select>\n              </el-form-item>\n            </div>\n\n            <div class=\"form-row\">\n              <el-form-item prop=\"mainSubject\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.mainSubject\"\n                    type=\"text\"\n                    placeholder=\"主学科（如：数学）\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"extraSubjects\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.extraSubjects\"\n                    type=\"text\"\n                    placeholder=\"副学科\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 教师专用信息 -->\n          <div v-if=\"registerForm.userType === 'teacher'\" class=\"form-section\">\n            <h4 class=\"section-title\">教学信息</h4>\n            \n            <el-form-item prop=\"certificateNo\">\n              <div class=\"input-group\">\n                <svg-icon icon-class=\"documentation\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"registerForm.certificateNo\"\n                  type=\"text\"\n                  placeholder=\"请输入教师资格证编号\"\n                  class=\"custom-input\"\n                />\n              </div>\n            </el-form-item>\n\n            <div class=\"form-row\">\n              <el-form-item prop=\"stages\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.stages\"\n                    type=\"text\"\n                    placeholder=\"教授学段（如：小学、初中、高中）\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"subjects\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.subjects\"\n                    type=\"text\"\n                    placeholder=\"教授学科（如：数学、语文、英语）\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 验证码 -->\n          <el-form-item prop=\"code\" v-if=\"captchaEnabled\" class=\"captcha-section\">\n            <div class=\"captcha-group\">\n              <div class=\"input-group captcha-input\">\n                <svg-icon icon-class=\"validCode\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"registerForm.code\"\n                  auto-complete=\"off\"\n                  placeholder=\"请输入验证码\"\n                  class=\"custom-input\"\n                  @keyup.enter.native=\"handleRegister\"\n                />\n              </div>\n              <div class=\"captcha-image\" @click=\"getCode\">\n                <img :src=\"codeUrl\" class=\"captcha-img\"/>\n                <div class=\"refresh-tip\">点击刷新</div>\n              </div>\n            </div>\n          </el-form-item>\n\n          <!-- 注册按钮 -->\n          <el-form-item class=\"register-btn-item\">\n            <el-button\n              :loading=\"loading\"\n              type=\"primary\"\n              class=\"register-btn\"\n              @click.native.prevent=\"handleRegister\"\n            >\n              <span v-if=\"!loading\">{{ getRegisterButtonText() }}</span>\n              <span v-else>注册中...</span>\n            </el-button>\n          </el-form-item>\n\n          <div class=\"login-link\">\n            <span>已有账号？</span>\n            <router-link to=\"/login\" class=\"link\">立即登录</router-link>\n          </div>\n        </el-form>\n      </div>\n\n      <!-- 装饰性元素 -->\n      <div class=\"decoration\">\n        <div class=\"circle circle-1\"></div>\n        <div class=\"circle circle-2\"></div>\n        <div class=\"circle circle-3\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg, register } from \"@/api/login\"\n\nexport default {\n  name: \"Register\",\n  data() {\n    const equalToPassword = (rule, value, callback) => {\n      if (this.registerForm.password !== value) {\n        callback(new Error(\"两次输入的密码不一致\"))\n      } else {\n        callback()\n      }\n    }\n    return {\n      title: process.env.VUE_APP_TITLE,\n      codeUrl: \"\",\n      registerForm: {\n        userType: this.$route.query.type || \"student\",\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        phone: \"\",\n        realName: \"\",\n        grade: \"\",\n        stage: \"\",\n        mainSubject: \"\",\n        extraSubjects: \"\",\n        certificateNo: \"\",\n        stages: \"\",\n        subjects: \"\",\n        code: \"\",\n        uuid: \"\"\n      },\n      identityTypes: [\n        { value: \"student\", label: \"学生\", icon: \"education\" },\n        { value: \"parent\", label: \"家长\", icon: \"peoples\" },\n        { value: \"teacher\", label: \"教师\", icon: \"user\" }\n      ],\n      registerRules: {\n        userType: [\n          { required: true, trigger: \"change\", message: \"请选择用户类型\" }\n        ],\n        username: [\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\n          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\n          { min: 5, max: 20, message: \"用户密码长度必须介于 5 和 20 之间\", trigger: \"blur\" },\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\ |\", trigger: \"blur\" }\n        ],\n        confirmPassword: [\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\n        ],\n        phone: [\n          { required: true, trigger: \"blur\", message: \"请输入手机号\" },\n          { pattern: /^1[3-9]\\d{9}$/, message: \"请输入正确的手机号格式\", trigger: \"blur\" }\n        ],\n        realName: [\n          { required: true, trigger: \"blur\", message: \"请输入真实姓名\" }\n        ],\n        grade: [\n          { required: true, trigger: \"change\", message: \"请选择年级\" }\n        ],\n        stage: [\n          { required: true, trigger: \"change\", message: \"请选择学段\" }\n        ],\n        mainSubject: [\n          { required: true, trigger: \"blur\", message: \"请输入主学科\" }\n        ],\n        certificateNo: [\n          { required: true, trigger: \"blur\", message: \"请输入教师资格证编号\" }\n        ],\n        stages: [\n          { required: true, trigger: \"blur\", message: \"请输入教授学段\" }\n        ],\n        subjects: [\n          { required: true, trigger: \"blur\", message: \"请输入教授学科\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      captchaEnabled: true\n    }\n  },\n  created() {\n    this.getCode()\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img\n          this.registerForm.uuid = res.uuid\n        }\n      })\n    },\n    switchIdentity(type) {\n      this.registerForm.userType = type\n      // 清空特定字段\n      this.registerForm.grade = \"\"\n      this.registerForm.stage = \"\"\n      this.registerForm.mainSubject = \"\"\n      this.registerForm.extraSubjects = \"\"\n      this.registerForm.certificateNo = \"\"\n      this.registerForm.stages = \"\"\n      this.registerForm.subjects = \"\"\n      \n      // 清除验证\n      this.$nextTick(() => {\n        this.$refs.registerForm.clearValidate()\n      })\n    },\n\n    getIdentityIcon() {\n      const type = this.identityTypes.find(t => t.value === this.registerForm.userType)\n      return type ? type.icon : 'user'\n    },\n    getIdentityTitle() {\n      const type = this.identityTypes.find(t => t.value === this.registerForm.userType)\n      return type ? `${type.label}注册` : '用户注册'\n    },\n    getIdentitySubtitle() {\n      const subtitles = {\n        student: '开启您的学习之旅',\n        parent: '关注孩子的学习成长',\n        teacher: '开启您的教学管理'\n      }\n      return subtitles[this.registerForm.userType] || '欢迎加入'\n    },\n    getRegisterButtonText() {\n      const texts = {\n        student: '开始学习',\n        parent: '开始管理',\n        teacher: '开始教学'\n      }\n      return texts[this.registerForm.userType] || '注册'\n    },\n    handleRegister() {\n      this.$refs.registerForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n          register(this.registerForm).then(res => {\n            const username = this.registerForm.username\n            const userType = this.registerForm.userType\n            this.$alert(\"<font color='red'>恭喜你，您的账号 \" + username + \" 注册成功！</font>\", '系统提示', {\n              dangerouslyUseHTMLString: true,\n              type: 'success'\n            }).then(() => {\n              // 跳转到登录页面并传递用户类型参数\n              this.$router.push({\n                path: \"/login\",\n                query: { type: userType }\n              })\n            }).catch(() => {})\n          }).catch(() => {\n            this.loading = false\n            if (this.captchaEnabled) {\n              this.getCode()\n            }\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\n.register {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: url(\"../assets/images/login-background.jpg\") center/cover;\n    opacity: 0.1;\n    z-index: 0;\n  }\n\n  // 添加动态背景粒子效果\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-image: \n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);\n    animation: backgroundShift 8s ease-in-out infinite;\n    z-index: 0;\n  }\n\n  .register-container {\n    position: relative;\n    z-index: 1;\n    width: 100%;\n    max-width: 600px;\n  }\n\n\n\n  .register-card {\n    background: rgba(255, 255, 255, 0.95);\n    border-radius: 24px;\n    padding: 45px;\n    box-shadow: \n      0 25px 50px rgba(0, 0, 0, 0.15),\n      0 10px 20px rgba(0, 0, 0, 0.1),\n      inset 0 1px 0 rgba(255, 255, 255, 0.6);\n    backdrop-filter: blur(15px);\n    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n    animation: slideInUp 0.8s ease-out;\n    position: relative;\n    overflow: hidden;\n    border: 1px solid rgba(255, 255, 255, 0.2);\n\n    // 光泽效果\n    &::before {\n      content: '';\n      position: absolute;\n      top: -50%;\n      left: -50%;\n      width: 200%;\n      height: 200%;\n      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);\n      transform: rotate(45deg);\n      transition: all 0.6s ease;\n      opacity: 0;\n      pointer-events: none;\n      z-index: 1;\n    }\n\n    // 边框光效\n    &::after {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      border-radius: 24px;\n      padding: 2px;\n      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n      mask-composite: exclude;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n      pointer-events: none;\n      z-index: 1;\n    }\n\n    &:hover {\n      transform: translateY(-8px) scale(1.02);\n      box-shadow: \n        0 35px 70px rgba(0, 0, 0, 0.25),\n        0 15px 30px rgba(0, 0, 0, 0.15),\n        inset 0 1px 0 rgba(255, 255, 255, 0.8);\n\n      &::before {\n        opacity: 1;\n        transform: rotate(45deg) translate(50%, 50%);\n      }\n\n      &::after {\n        opacity: 1;\n      }\n    }\n\n    .card-header {\n      text-align: center;\n      margin-bottom: 30px;\n\n      .identity-icon {\n        width: 80px;\n        height: 80px;\n        margin: 0 auto 20px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.3s ease;\n\n        .icon {\n          font-size: 2.5rem;\n        }\n      }\n\n      .card-title {\n        font-size: 1.8rem;\n        font-weight: 600;\n        margin: 0 0 10px 0;\n        color: #333;\n      }\n\n      .card-subtitle {\n        font-size: 1rem;\n        color: #666;\n        margin: 0;\n      }\n    }\n\n    .identity-switch {\n      display: flex;\n      background: rgba(245, 245, 245, 0.8);\n      border-radius: 16px;\n      padding: 6px;\n      margin-bottom: 35px;\n      backdrop-filter: blur(5px);\n      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);\n\n      .switch-item {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 14px 10px;\n        border-radius: 12px;\n        cursor: pointer;\n        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n        font-size: 15px;\n        font-weight: 600;\n        position: relative;\n        overflow: hidden;\n\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));\n          border-radius: 12px;\n          opacity: 0;\n          transition: opacity 0.3s ease;\n          pointer-events: none;\n        }\n\n        .switch-icon {\n          margin-right: 8px;\n          font-size: 18px;\n          transition: all 0.3s ease;\n        }\n\n        &.active {\n          background: #fff;\n          color: #409EFF;\n          box-shadow: \n            0 4px 12px rgba(0, 0, 0, 0.15),\n            0 2px 4px rgba(64, 158, 255, 0.2);\n          transform: translateY(-2px);\n\n          &::before {\n            opacity: 1;\n          }\n\n          .switch-icon {\n            transform: scale(1.1);\n          }\n        }\n\n        &:hover:not(.active) {\n          background: rgba(255, 255, 255, 0.9);\n          transform: translateY(-1px);\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n        }\n      }\n    }\n\n    .register-form {\n      position: relative;\n      z-index: 2;\n      \n      .form-section {\n        margin-bottom: 30px;\n\n        .section-title {\n          font-size: 1.1rem;\n          font-weight: 600;\n          color: #333;\n          margin: 0 0 20px 0;\n          padding-bottom: 10px;\n          border-bottom: 2px solid #f0f0f0;\n        }\n\n        .form-row {\n          display: flex;\n          gap: 15px;\n          margin-bottom: 20px;\n\n          .form-item-half {\n            flex: 1;\n            margin-bottom: 0;\n          }\n        }\n\n        .input-group {\n          position: relative;\n          margin-bottom: 25px;\n\n          .input-icon {\n            position: absolute;\n            left: 20px;\n            top: 50%;\n            transform: translateY(-50%);\n            color: #999;\n            font-size: 16px;\n            z-index: 3;\n            transition: all 0.3s ease;\n          }\n\n          .custom-input {\n            ::v-deep .el-input__inner {\n              height: 55px !important;\n              padding-left: 60px !important;\n              border: 2px solid #e8e8e8 !important;\n              border-radius: 16px !important;\n              font-size: 16px !important;\n              transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;\n              background: rgba(255, 255, 255, 0.9) !important;\n              backdrop-filter: blur(5px);\n              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;\n\n              &:focus {\n                border-color: #409EFF !important;\n                box-shadow: \n                  0 0 0 4px rgba(64, 158, 255, 0.15),\n                  0 4px 12px rgba(64, 158, 255, 0.1) !important;\n                background: rgba(255, 255, 255, 1) !important;\n                transform: translateY(-2px) !important;\n              }\n\n              &:hover {\n                border-color: #c0c4cc !important;\n                transform: translateY(-1px) !important;\n                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;\n              }\n            }\n          }\n\n          &:hover .input-icon {\n            color: #666;\n            transform: translateY(-50%) scale(1.1);\n          }\n        }\n\n        .custom-select {\n          width: 100%;\n\n          .el-input__inner {\n            height: 55px;\n            border: 2px solid #e8e8e8;\n            border-radius: 16px;\n            font-size: 16px;\n            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n            background: rgba(255, 255, 255, 0.9);\n            backdrop-filter: blur(5px);\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n\n            &:focus {\n              border-color: #409EFF;\n              box-shadow: \n                0 0 0 4px rgba(64, 158, 255, 0.15),\n                0 4px 12px rgba(64, 158, 255, 0.1);\n              background: rgba(255, 255, 255, 1);\n              transform: translateY(-2px);\n            }\n\n            &:hover {\n              border-color: #c0c4cc;\n              transform: translateY(-1px);\n              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n            }\n          }\n        }\n      }\n\n      .captcha-section {\n        margin-bottom: 30px;\n\n        .captcha-group {\n          display: flex;\n          align-items: center;\n          gap: 15px;\n\n          .captcha-input {\n            flex: 1;\n            margin-bottom: 0;\n          }\n\n          .captcha-image {\n            width: 120px;\n            height: 55px;\n            border-radius: 16px;\n            overflow: hidden;\n            cursor: pointer;\n            position: relative;\n            border: 2px solid #e8e8e8;\n            transition: all 0.3s ease;\n            background: rgba(255, 255, 255, 0.9);\n            backdrop-filter: blur(5px);\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n\n            &:hover {\n              border-color: #409EFF;\n              transform: scale(1.02);\n              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n            }\n\n            .captcha-img {\n              width: 100%;\n              height: 100%;\n              object-fit: cover;\n            }\n\n            .refresh-tip {\n              position: absolute;\n              bottom: 0;\n              left: 0;\n              right: 0;\n              background: rgba(0, 0, 0, 0.7);\n              color: #fff;\n              font-size: 12px;\n              text-align: center;\n              padding: 2px;\n              opacity: 0;\n              transition: opacity 0.3s ease;\n            }\n\n            &:hover .refresh-tip {\n              opacity: 1;\n            }\n          }\n        }\n      }\n\n      .register-btn-item {\n        margin-bottom: 25px;\n\n        .register-btn {\n          width: 100%;\n          height: 55px;\n          border-radius: 16px;\n          font-size: 17px;\n          font-weight: 700;\n          transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n          position: relative;\n          overflow: hidden;\n          border: none;\n          background: linear-gradient(135deg, #409EFF, #66b1ff);\n          box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);\n\n          &::before {\n            content: '';\n            position: absolute;\n            top: 0;\n            left: -100%;\n            width: 100%;\n            height: 100%;\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n            transition: left 0.5s ease;\n          }\n\n          &:hover {\n            transform: translateY(-3px) scale(1.02);\n            box-shadow: 0 12px 30px rgba(64, 158, 255, 0.4);\n            background: linear-gradient(135deg, #66b1ff, #409EFF);\n\n            &::before {\n              left: 100%;\n            }\n          }\n\n          &:active {\n            transform: translateY(-1px) scale(0.98);\n          }\n        }\n      }\n\n      .login-link {\n        text-align: center;\n        color: #666;\n        font-size: 14px;\n\n        .link {\n          color: #409EFF;\n          text-decoration: none;\n          font-weight: 500;\n          margin-left: 5px;\n          transition: color 0.3s ease;\n\n          &:hover {\n            color: #66b1ff;\n          }\n        }\n      }\n    }\n\n    // 主题样式\n    &.student-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #409EFF, #66b1ff);\n        color: #fff;\n      }\n    }\n\n    &.parent-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #67C23A, #85ce61);\n        color: #fff;\n      }\n    }\n\n    &.teacher-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #E6A23C, #f0c78a);\n        color: #fff;\n      }\n    }\n  }\n\n  .decoration {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    pointer-events: none;\n    z-index: 0;\n\n    .circle {\n      position: absolute;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      animation: float 6s ease-in-out infinite;\n\n      &.circle-1 {\n        width: 100px;\n        height: 100px;\n        top: 20%;\n        left: 10%;\n        animation-delay: 0s;\n      }\n\n      &.circle-2 {\n        width: 150px;\n        height: 150px;\n        top: 60%;\n        right: 10%;\n        animation-delay: 2s;\n      }\n\n      &.circle-3 {\n        width: 80px;\n        height: 80px;\n        bottom: 20%;\n        left: 20%;\n        animation-delay: 4s;\n      }\n    }\n  }\n}\n\n// 动画定义\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n@keyframes backgroundShift {\n  0%, 100% {\n    transform: translateX(0) translateY(0);\n  }\n  25% {\n    transform: translateX(-15px) translateY(-8px);\n  }\n  50% {\n    transform: translateX(15px) translateY(8px);\n  }\n  75% {\n    transform: translateX(-8px) translateY(15px);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n@keyframes shimmer {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .register {\n    padding: 10px;\n\n    .register-container {\n      max-width: 100%;\n    }\n\n    .register-card {\n      padding: 30px 20px;\n      margin-top: 40px;\n\n      .card-header .identity-icon {\n        width: 60px;\n        height: 60px;\n\n        .icon {\n          font-size: 2rem;\n        }\n      }\n\n      .identity-switch {\n        .switch-item {\n          padding: 10px 4px;\n          font-size: 12px;\n\n          .switch-icon {\n            font-size: 14px;\n          }\n        }\n      }\n\n      .register-form {\n        .form-section {\n          .form-row {\n            flex-direction: column;\n            gap: 0;\n\n            .form-item-half {\n              margin-bottom: 20px;\n            }\n          }\n        }\n      }\n    }\n\n\n  }\n}\n</style>\n"]}]}