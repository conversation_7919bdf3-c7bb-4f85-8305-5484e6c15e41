{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\register.vue?vue&type=style&index=0&id=77453986&rel=stylesheet%2Fscss&lang=scss&scoped=true", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\register.vue", "mtime": 1758268597404}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758264042901}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758264044469}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758264043503}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758264042403}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758264043933}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnJlZ2lzdGVyIHsKICBtaW4taGVpZ2h0OiAxMDB2aDsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBwYWRkaW5nOiAyMHB4OwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICBvdmVyZmxvdzogaGlkZGVuOwoKICAmOjpiZWZvcmUgewogICAgY29udGVudDogJyc7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB0b3A6IDA7CiAgICBsZWZ0OiAwOwogICAgcmlnaHQ6IDA7CiAgICBib3R0b206IDA7CiAgICBiYWNrZ3JvdW5kOiB1cmwoIi4uL2Fzc2V0cy9pbWFnZXMvbG9naW4tYmFja2dyb3VuZC5qcGciKSBjZW50ZXIvY292ZXI7CiAgICBvcGFjaXR5OiAwLjE7CiAgICB6LWluZGV4OiAwOwogIH0KCiAgLy8g5re75Yqg5Yqo5oCB6IOM5pmv57KS5a2Q5pWI5p6cCiAgJjo6YWZ0ZXIgewogICAgY29udGVudDogJyc7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB0b3A6IDA7CiAgICBsZWZ0OiAwOwogICAgcmlnaHQ6IDA7CiAgICBib3R0b206IDA7CiAgICBiYWNrZ3JvdW5kLWltYWdlOiAKICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAyMCUgODAlLCByZ2JhKDEyMCwgMTE5LCAxOTgsIDAuMykgMCUsIHRyYW5zcGFyZW50IDUwJSksCiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgODAlIDIwJSwgcmdiYSgyNTUsIDExOSwgMTk4LCAwLjMpIDAlLCB0cmFuc3BhcmVudCA1MCUpLAogICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDQwJSA0MCUsIHJnYmEoMTIwLCAyMTksIDI1NSwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKTsKICAgIGFuaW1hdGlvbjogYmFja2dyb3VuZFNoaWZ0IDhzIGVhc2UtaW4tb3V0IGluZmluaXRlOwogICAgei1pbmRleDogMDsKICB9CgogIC5yZWdpc3Rlci1jb250YWluZXIgewogICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgei1pbmRleDogMTsKICAgIHdpZHRoOiAxMDAlOwogICAgbWF4LXdpZHRoOiA2MDBweDsKICB9CgogIC5iYWNrLWJ0biB7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB0b3A6IC02MHB4OwogICAgbGVmdDogMDsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsKICAgIGN1cnNvcjogcG9pbnRlcjsKICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBmb250LXdlaWdodDogNTAwOwoKICAgICY6aG92ZXIgewogICAgICBjb2xvcjogI2ZmZjsKICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01cHgpOwogICAgfQoKICAgIC5iYWNrLWljb24gewogICAgICBtYXJnaW4tcmlnaHQ6IDhweDsKICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgfQogIH0KCiAgLnJlZ2lzdGVyLWNhcmQgewogICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTsKICAgIGJvcmRlci1yYWRpdXM6IDI0cHg7CiAgICBwYWRkaW5nOiA0NXB4OwogICAgYm94LXNoYWRvdzogCiAgICAgIDAgMjVweCA1MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSksCiAgICAgIDAgMTBweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4xKSwKICAgICAgaW5zZXQgMCAxcHggMCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNik7CiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTVweCk7CiAgICB0cmFuc2l0aW9uOiBhbGwgMC40cyBjdWJpYy1iZXppZXIoMC4xNzUsIDAuODg1LCAwLjMyLCAxLjI3NSk7CiAgICBhbmltYXRpb246IHNsaWRlSW5VcCAwLjhzIGVhc2Utb3V0OwogICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTsKCiAgICAvLyDlhYnms73mlYjmnpwKICAgICY6OmJlZm9yZSB7CiAgICAgIGNvbnRlbnQ6ICcnOwogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgIHRvcDogLTUwJTsKICAgICAgbGVmdDogLTUwJTsKICAgICAgd2lkdGg6IDIwMCU7CiAgICAgIGhlaWdodDogMjAwJTsKICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCB0cmFuc3BhcmVudCwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpLCB0cmFuc3BhcmVudCk7CiAgICAgIHRyYW5zZm9ybTogcm90YXRlKDQ1ZGVnKTsKICAgICAgdHJhbnNpdGlvbjogYWxsIDAuNnMgZWFzZTsKICAgICAgb3BhY2l0eTogMDsKICAgICAgcG9pbnRlci1ldmVudHM6IG5vbmU7CiAgICAgIHotaW5kZXg6IDE7CiAgICB9CgogICAgLy8g6L655qGG5YWJ5pWICiAgICAmOjphZnRlciB7CiAgICAgIGNvbnRlbnQ6ICcnOwogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgIHRvcDogMDsKICAgICAgbGVmdDogMDsKICAgICAgcmlnaHQ6IDA7CiAgICAgIGJvdHRvbTogMDsKICAgICAgYm9yZGVyLXJhZGl1czogMjRweDsKICAgICAgcGFkZGluZzogMnB4OwogICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoNDVkZWcsIHRyYW5zcGFyZW50LCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyksIHRyYW5zcGFyZW50KTsKICAgICAgbWFzazogbGluZWFyLWdyYWRpZW50KCNmZmYgMCAwKSBjb250ZW50LWJveCwgbGluZWFyLWdyYWRpZW50KCNmZmYgMCAwKTsKICAgICAgbWFzay1jb21wb3NpdGU6IGV4Y2x1ZGU7CiAgICAgIG9wYWNpdHk6IDA7CiAgICAgIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zcyBlYXNlOwogICAgICBwb2ludGVyLWV2ZW50czogbm9uZTsKICAgICAgei1pbmRleDogMTsKICAgIH0KCiAgICAmOmhvdmVyIHsKICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC04cHgpIHNjYWxlKDEuMDIpOwogICAgICBib3gtc2hhZG93OiAKICAgICAgICAwIDM1cHggNzBweCByZ2JhKDAsIDAsIDAsIDAuMjUpLAogICAgICAgIDAgMTVweCAzMHB4IHJnYmEoMCwgMCwgMCwgMC4xNSksCiAgICAgICAgaW5zZXQgMCAxcHggMCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7CgogICAgICAmOjpiZWZvcmUgewogICAgICAgIG9wYWNpdHk6IDE7CiAgICAgICAgdHJhbnNmb3JtOiByb3RhdGUoNDVkZWcpIHRyYW5zbGF0ZSg1MCUsIDUwJSk7CiAgICAgIH0KCiAgICAgICY6OmFmdGVyIHsKICAgICAgICBvcGFjaXR5OiAxOwogICAgICB9CiAgICB9CgogICAgLmNhcmQtaGVhZGVyIHsKICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICBtYXJnaW4tYm90dG9tOiAzMHB4OwoKICAgICAgLmlkZW50aXR5LWljb24gewogICAgICAgIHdpZHRoOiA4MHB4OwogICAgICAgIGhlaWdodDogODBweDsKICAgICAgICBtYXJnaW46IDAgYXV0byAyMHB4OwogICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKCiAgICAgICAgLmljb24gewogICAgICAgICAgZm9udC1zaXplOiAyLjVyZW07CiAgICAgICAgfQogICAgICB9CgogICAgICAuY2FyZC10aXRsZSB7CiAgICAgICAgZm9udC1zaXplOiAxLjhyZW07CiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsKICAgICAgICBtYXJnaW46IDAgMCAxMHB4IDA7CiAgICAgICAgY29sb3I6ICMzMzM7CiAgICAgIH0KCiAgICAgIC5jYXJkLXN1YnRpdGxlIHsKICAgICAgICBmb250LXNpemU6IDFyZW07CiAgICAgICAgY29sb3I6ICM2NjY7CiAgICAgICAgbWFyZ2luOiAwOwogICAgICB9CiAgICB9CgogICAgLmlkZW50aXR5LXN3aXRjaCB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjQ1LCAyNDUsIDI0NSwgMC44KTsKICAgICAgYm9yZGVyLXJhZGl1czogMTZweDsKICAgICAgcGFkZGluZzogNnB4OwogICAgICBtYXJnaW4tYm90dG9tOiAzNXB4OwogICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoNXB4KTsKICAgICAgYm94LXNoYWRvdzogaW5zZXQgMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7CgogICAgICAuc3dpdGNoLWl0ZW0gewogICAgICAgIGZsZXg6IDE7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgICAgIHBhZGRpbmc6IDE0cHggMTBweDsKICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4OwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC40cyBjdWJpYy1iZXppZXIoMC4xNzUsIDAuODg1LCAwLjMyLCAxLjI3NSk7CiAgICAgICAgZm9udC1zaXplOiAxNXB4OwogICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7CiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICAgIG92ZXJmbG93OiBoaWRkZW47CgogICAgICAgICY6OmJlZm9yZSB7CiAgICAgICAgICBjb250ZW50OiAnJzsKICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgICAgIHRvcDogMDsKICAgICAgICAgIGxlZnQ6IDA7CiAgICAgICAgICByaWdodDogMDsKICAgICAgICAgIGJvdHRvbTogMDsKICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjYpKTsKICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7CiAgICAgICAgICBvcGFjaXR5OiAwOwogICAgICAgICAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjNzIGVhc2U7CiAgICAgICAgICBwb2ludGVyLWV2ZW50czogbm9uZTsKICAgICAgICB9CgogICAgICAgIC5zd2l0Y2gtaWNvbiB7CiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDsKICAgICAgICAgIGZvbnQtc2l6ZTogMThweDsKICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7CiAgICAgICAgfQoKICAgICAgICAmLmFjdGl2ZSB7CiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOwogICAgICAgICAgY29sb3I6ICM0MDlFRkY7CiAgICAgICAgICBib3gtc2hhZG93OiAKICAgICAgICAgICAgMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpLAogICAgICAgICAgICAwIDJweCA0cHggcmdiYSg2NCwgMTU4LCAyNTUsIDAuMik7CiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7CgogICAgICAgICAgJjo6YmVmb3JlIHsKICAgICAgICAgICAgb3BhY2l0eTogMTsKICAgICAgICAgIH0KCiAgICAgICAgICAuc3dpdGNoLWljb24gewogICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICAmOmhvdmVyOm5vdCguYWN0aXZlKSB7CiAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7CiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7CiAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOwogICAgICAgIH0KICAgICAgfQogICAgfQoKICAgIC5yZWdpc3Rlci1mb3JtIHsKICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICB6LWluZGV4OiAyOwogICAgICAKICAgICAgLmZvcm0tc2VjdGlvbiB7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDsKCiAgICAgICAgLnNlY3Rpb24tdGl0bGUgewogICAgICAgICAgZm9udC1zaXplOiAxLjFyZW07CiAgICAgICAgICBmb250LXdlaWdodDogNjAwOwogICAgICAgICAgY29sb3I6ICMzMzM7CiAgICAgICAgICBtYXJnaW46IDAgMCAyMHB4IDA7CiAgICAgICAgICBwYWRkaW5nLWJvdHRvbTogMTBweDsKICAgICAgICAgIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjZjBmMGYwOwogICAgICAgIH0KCiAgICAgICAgLmZvcm0tcm93IHsKICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICBnYXA6IDE1cHg7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwoKICAgICAgICAgIC5mb3JtLWl0ZW0taGFsZiB7CiAgICAgICAgICAgIGZsZXg6IDE7CiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDA7CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICAuaW5wdXQtZ3JvdXAgewogICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICAgICAgbWFyZ2luLWJvdHRvbTogMjVweDsKCiAgICAgICAgICAuaW5wdXQtaWNvbiB7CiAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgICAgICAgbGVmdDogMjBweDsKICAgICAgICAgICAgdG9wOiA1MCU7CiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKTsKICAgICAgICAgICAgY29sb3I6ICM5OTk7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgICAgICAgei1pbmRleDogMzsKICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKICAgICAgICAgIH0KCiAgICAgICAgICAuY3VzdG9tLWlucHV0IHsKICAgICAgICAgICAgOjp2LWRlZXAgLmVsLWlucHV0X19pbm5lciB7CiAgICAgICAgICAgICAgaGVpZ2h0OiA1NXB4ICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgcGFkZGluZy1sZWZ0OiA2MHB4ICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgYm9yZGVyOiAycHggc29saWQgI2U4ZThlOCAhaW1wb3J0YW50OwogICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDE2cHggIWltcG9ydGFudDsKICAgICAgICAgICAgICBmb250LXNpemU6IDE2cHggIWltcG9ydGFudDsKICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC40cyBjdWJpYy1iZXppZXIoMC4xNzUsIDAuODg1LCAwLjMyLCAxLjI3NSkgIWltcG9ydGFudDsKICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSkgIWltcG9ydGFudDsKICAgICAgICAgICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoNXB4KTsKICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjA1KSAhaW1wb3J0YW50OwoKICAgICAgICAgICAgICAmOmZvY3VzIHsKICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzQwOUVGRiAhaW1wb3J0YW50OwogICAgICAgICAgICAgICAgYm94LXNoYWRvdzogCiAgICAgICAgICAgICAgICAgIDAgMCAwIDRweCByZ2JhKDY0LCAxNTgsIDI1NSwgMC4xNSksCiAgICAgICAgICAgICAgICAgIDAgNHB4IDEycHggcmdiYSg2NCwgMTU4LCAyNTUsIDAuMSkgIWltcG9ydGFudDsKICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMSkgIWltcG9ydGFudDsKICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KSAhaW1wb3J0YW50OwogICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICNjMGM0Y2MgIWltcG9ydGFudDsKICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KSAhaW1wb3J0YW50OwogICAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMDgpICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CgogICAgICAgICAgJjpob3ZlciAuaW5wdXQtaWNvbiB7CiAgICAgICAgICAgIGNvbG9yOiAjNjY2OwogICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSkgc2NhbGUoMS4xKTsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC5jdXN0b20tc2VsZWN0IHsKICAgICAgICAgIHdpZHRoOiAxMDAlOwoKICAgICAgICAgIC5lbC1pbnB1dF9faW5uZXIgewogICAgICAgICAgICBoZWlnaHQ6IDU1cHg7CiAgICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICNlOGU4ZTg7CiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDE2cHg7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuNHMgY3ViaWMtYmV6aWVyKDAuMTc1LCAwLjg4NSwgMC4zMiwgMS4yNzUpOwogICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7CiAgICAgICAgICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cig1cHgpOwogICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjA1KTsKCiAgICAgICAgICAgICY6Zm9jdXMgewogICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzQwOUVGRjsKICAgICAgICAgICAgICBib3gtc2hhZG93OiAKICAgICAgICAgICAgICAgIDAgMCAwIDRweCByZ2JhKDY0LCAxNTgsIDI1NSwgMC4xNSksCiAgICAgICAgICAgICAgICAwIDRweCAxMnB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjEpOwogICAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMSk7CiAgICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOwogICAgICAgICAgICB9CgogICAgICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICNjMGM0Y2M7CiAgICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpOwogICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjA4KTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgLmNhcHRjaGEtc2VjdGlvbiB7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDsKCiAgICAgICAgLmNhcHRjaGEtZ3JvdXAgewogICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgICBnYXA6IDE1cHg7CgogICAgICAgICAgLmNhcHRjaGEtaW5wdXQgewogICAgICAgICAgICBmbGV4OiAxOwogICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwOwogICAgICAgICAgfQoKICAgICAgICAgIC5jYXB0Y2hhLWltYWdlIHsKICAgICAgICAgICAgd2lkdGg6IDEyMHB4OwogICAgICAgICAgICBoZWlnaHQ6IDU1cHg7CiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDE2cHg7CiAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCAjZThlOGU4OwogICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwogICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7CiAgICAgICAgICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cig1cHgpOwogICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjA1KTsKCiAgICAgICAgICAgICY6aG92ZXIgewogICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzQwOUVGRjsKICAgICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDIpOwogICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjA4KTsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgLmNhcHRjaGEtaW1nIHsKICAgICAgICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICAgICAgICBoZWlnaHQ6IDEwMCU7CiAgICAgICAgICAgICAgb2JqZWN0LWZpdDogY292ZXI7CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC5yZWZyZXNoLXRpcCB7CiAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICAgICAgICAgIGJvdHRvbTogMDsKICAgICAgICAgICAgICBsZWZ0OiAwOwogICAgICAgICAgICAgIHJpZ2h0OiAwOwogICAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC43KTsKICAgICAgICAgICAgICBjb2xvcjogI2ZmZjsKICAgICAgICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICAgICAgICAgIHBhZGRpbmc6IDJweDsKICAgICAgICAgICAgICBvcGFjaXR5OiAwOwogICAgICAgICAgICAgIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zcyBlYXNlOwogICAgICAgICAgICB9CgogICAgICAgICAgICAmOmhvdmVyIC5yZWZyZXNoLXRpcCB7CiAgICAgICAgICAgICAgb3BhY2l0eTogMTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgLnJlZ2lzdGVyLWJ0bi1pdGVtIHsKICAgICAgICBtYXJnaW4tYm90dG9tOiAyNXB4OwoKICAgICAgICAucmVnaXN0ZXItYnRuIHsKICAgICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgICAgaGVpZ2h0OiA1NXB4OwogICAgICAgICAgYm9yZGVyLXJhZGl1czogMTZweDsKICAgICAgICAgIGZvbnQtc2l6ZTogMTdweDsKICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7CiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC40cyBjdWJpYy1iZXppZXIoMC4xNzUsIDAuODg1LCAwLjMyLCAxLjI3NSk7CiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgICAgICAgYm9yZGVyOiBub25lOwogICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzQwOUVGRiwgIzY2YjFmZik7CiAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxNXB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjMpOwoKICAgICAgICAgICY6OmJlZm9yZSB7CiAgICAgICAgICAgIGNvbnRlbnQ6ICcnOwogICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgICAgICAgIHRvcDogMDsKICAgICAgICAgICAgbGVmdDogLTEwMCU7CiAgICAgICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgICAgICBoZWlnaHQ6IDEwMCU7CiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgdHJhbnNwYXJlbnQsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKSwgdHJhbnNwYXJlbnQpOwogICAgICAgICAgICB0cmFuc2l0aW9uOiBsZWZ0IDAuNXMgZWFzZTsKICAgICAgICAgIH0KCiAgICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0zcHgpIHNjYWxlKDEuMDIpOwogICAgICAgICAgICBib3gtc2hhZG93OiAwIDEycHggMzBweCByZ2JhKDY0LCAxNTgsIDI1NSwgMC40KTsKICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2YjFmZiwgIzQwOUVGRik7CgogICAgICAgICAgICAmOjpiZWZvcmUgewogICAgICAgICAgICAgIGxlZnQ6IDEwMCU7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KCiAgICAgICAgICAmOmFjdGl2ZSB7CiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KSBzY2FsZSgwLjk4KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5sb2dpbi1saW5rIHsKICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgICAgY29sb3I6ICM2NjY7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwoKICAgICAgICAubGluayB7CiAgICAgICAgICBjb2xvcjogIzQwOUVGRjsKICAgICAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTsKICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgICAgICAgICBtYXJnaW4tbGVmdDogNXB4OwogICAgICAgICAgdHJhbnNpdGlvbjogY29sb3IgMC4zcyBlYXNlOwoKICAgICAgICAgICY6aG92ZXIgewogICAgICAgICAgICBjb2xvcjogIzY2YjFmZjsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0KCiAgICAvLyDkuLvpopjmoLflvI8KICAgICYuc3R1ZGVudC10aGVtZSB7CiAgICAgIC5jYXJkLWhlYWRlciAuaWRlbnRpdHktaWNvbiB7CiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzQwOUVGRiwgIzY2YjFmZik7CiAgICAgICAgY29sb3I6ICNmZmY7CiAgICAgIH0KICAgIH0KCiAgICAmLnBhcmVudC10aGVtZSB7CiAgICAgIC5jYXJkLWhlYWRlciAuaWRlbnRpdHktaWNvbiB7CiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY3QzIzQSwgIzg1Y2U2MSk7CiAgICAgICAgY29sb3I6ICNmZmY7CiAgICAgIH0KICAgIH0KCiAgICAmLnRlYWNoZXItdGhlbWUgewogICAgICAuY2FyZC1oZWFkZXIgLmlkZW50aXR5LWljb24gewogICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNFNkEyM0MsICNmMGM3OGEpOwogICAgICAgIGNvbG9yOiAjZmZmOwogICAgICB9CiAgICB9CiAgfQoKICAuZGVjb3JhdGlvbiB7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB0b3A6IDA7CiAgICBsZWZ0OiAwOwogICAgcmlnaHQ6IDA7CiAgICBib3R0b206IDA7CiAgICBwb2ludGVyLWV2ZW50czogbm9uZTsKICAgIHotaW5kZXg6IDA7CgogICAgLmNpcmNsZSB7CiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgYm9yZGVyLXJhZGl1czogNTAlOwogICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7CiAgICAgIGFuaW1hdGlvbjogZmxvYXQgNnMgZWFzZS1pbi1vdXQgaW5maW5pdGU7CgogICAgICAmLmNpcmNsZS0xIHsKICAgICAgICB3aWR0aDogMTAwcHg7CiAgICAgICAgaGVpZ2h0OiAxMDBweDsKICAgICAgICB0b3A6IDIwJTsKICAgICAgICBsZWZ0OiAxMCU7CiAgICAgICAgYW5pbWF0aW9uLWRlbGF5OiAwczsKICAgICAgfQoKICAgICAgJi5jaXJjbGUtMiB7CiAgICAgICAgd2lkdGg6IDE1MHB4OwogICAgICAgIGhlaWdodDogMTUwcHg7CiAgICAgICAgdG9wOiA2MCU7CiAgICAgICAgcmlnaHQ6IDEwJTsKICAgICAgICBhbmltYXRpb24tZGVsYXk6IDJzOwogICAgICB9CgogICAgICAmLmNpcmNsZS0zIHsKICAgICAgICB3aWR0aDogODBweDsKICAgICAgICBoZWlnaHQ6IDgwcHg7CiAgICAgICAgYm90dG9tOiAyMCU7CiAgICAgICAgbGVmdDogMjAlOwogICAgICAgIGFuaW1hdGlvbi1kZWxheTogNHM7CiAgICAgIH0KICAgIH0KICB9Cn0KCi8vIOWKqOeUu+WumuS5iQpAa2V5ZnJhbWVzIHNsaWRlSW5VcCB7CiAgZnJvbSB7CiAgICBvcGFjaXR5OiAwOwogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDMwcHgpOwogIH0KICB0byB7CiAgICBvcGFjaXR5OiAxOwogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApOwogIH0KfQoKQGtleWZyYW1lcyBmbG9hdCB7CiAgMCUsIDEwMCUgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDBweCk7CiAgfQogIDUwJSB7CiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTIwcHgpOwogIH0KfQoKQGtleWZyYW1lcyBiYWNrZ3JvdW5kU2hpZnQgewogIDAlLCAxMDAlIHsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKSB0cmFuc2xhdGVZKDApOwogIH0KICAyNSUgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC0xNXB4KSB0cmFuc2xhdGVZKC04cHgpOwogIH0KICA1MCUgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDE1cHgpIHRyYW5zbGF0ZVkoOHB4KTsKICB9CiAgNzUlIHsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtOHB4KSB0cmFuc2xhdGVZKDE1cHgpOwogIH0KfQoKQGtleWZyYW1lcyBwdWxzZSB7CiAgMCUsIDEwMCUgewogICAgdHJhbnNmb3JtOiBzY2FsZSgxKTsKICB9CiAgNTAlIHsKICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7CiAgfQp9CgpAa2V5ZnJhbWVzIHNoaW1tZXIgewogIDAlIHsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtMTAwJSk7CiAgfQogIDEwMCUgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDEwMCUpOwogIH0KfQoKLy8g5ZON5bqU5byP6K6+6K6hCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgewogIC5yZWdpc3RlciB7CiAgICBwYWRkaW5nOiAxMHB4OwoKICAgIC5yZWdpc3Rlci1jb250YWluZXIgewogICAgICBtYXgtd2lkdGg6IDEwMCU7CiAgICB9CgogICAgLnJlZ2lzdGVyLWNhcmQgewogICAgICBwYWRkaW5nOiAzMHB4IDIwcHg7CiAgICAgIG1hcmdpbi10b3A6IDQwcHg7CgogICAgICAuY2FyZC1oZWFkZXIgLmlkZW50aXR5LWljb24gewogICAgICAgIHdpZHRoOiA2MHB4OwogICAgICAgIGhlaWdodDogNjBweDsKCiAgICAgICAgLmljb24gewogICAgICAgICAgZm9udC1zaXplOiAycmVtOwogICAgICAgIH0KICAgICAgfQoKICAgICAgLmlkZW50aXR5LXN3aXRjaCB7CiAgICAgICAgLnN3aXRjaC1pdGVtIHsKICAgICAgICAgIHBhZGRpbmc6IDEwcHggNHB4OwogICAgICAgICAgZm9udC1zaXplOiAxMnB4OwoKICAgICAgICAgIC5zd2l0Y2gtaWNvbiB7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5yZWdpc3Rlci1mb3JtIHsKICAgICAgICAuZm9ybS1zZWN0aW9uIHsKICAgICAgICAgIC5mb3JtLXJvdyB7CiAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgICAgIGdhcDogMDsKCiAgICAgICAgICAgIC5mb3JtLWl0ZW0taGFsZiB7CiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQoKICAgIC5iYWNrLWJ0biB7CiAgICAgIHRvcDogLTUwcHg7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6aA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"register\">\n    <div class=\"register-container\">\n      <!-- 返回按钮 -->\n      <div class=\"back-btn\" @click=\"goBack\">\n        <svg-icon icon-class=\"arrow-left\" class=\"back-icon\" />\n        <span>返回选择</span>\n      </div>\n\n      <!-- 注册卡片 -->\n      <div class=\"register-card\" :class=\"`${registerForm.userType}-theme`\">\n        <div class=\"card-header\">\n          <div class=\"identity-icon\">\n            <svg-icon :icon-class=\"getIdentityIcon()\" class=\"icon\" />\n          </div>\n          <h2 class=\"card-title\">{{ getIdentityTitle() }}</h2>\n          <p class=\"card-subtitle\">{{ getIdentitySubtitle() }}</p>\n        </div>\n\n        <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\n          <!-- 身份切换 -->\n          <div class=\"identity-switch\">\n            <div \n              v-for=\"type in identityTypes\" \n              :key=\"type.value\"\n              class=\"switch-item\"\n              :class=\"{ active: registerForm.userType === type.value }\"\n              @click=\"switchIdentity(type.value)\"\n            >\n              <svg-icon :icon-class=\"type.icon\" class=\"switch-icon\" />\n              <span>{{ type.label }}</span>\n            </div>\n          </div>\n\n          <!-- 基本信息 -->\n          <div class=\"form-section\">\n            <h4 class=\"section-title\">基本信息</h4>\n            \n            <div class=\"form-row\">\n              <el-form-item prop=\"username\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"user\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.username\"\n                    type=\"text\"\n                    auto-complete=\"off\"\n                    placeholder=\"请输入账号\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"phone\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"phone\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.phone\"\n                    type=\"text\"\n                    auto-complete=\"off\"\n                    placeholder=\"请输入手机号\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n\n            <el-form-item prop=\"realName\">\n              <div class=\"input-group\">\n                <svg-icon icon-class=\"user\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"registerForm.realName\"\n                  type=\"text\"\n                  auto-complete=\"off\"\n                  placeholder=\"请输入真实姓名\"\n                  class=\"custom-input\"\n                />\n              </div>\n            </el-form-item>\n\n            <div class=\"form-row\">\n              <el-form-item prop=\"password\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"password\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.password\"\n                    type=\"password\"\n                    auto-complete=\"off\"\n                    placeholder=\"请输入密码\"\n                    class=\"custom-input\"\n                    @keyup.enter.native=\"handleRegister\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"confirmPassword\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"password\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.confirmPassword\"\n                    type=\"password\"\n                    auto-complete=\"off\"\n                    placeholder=\"确认密码\"\n                    class=\"custom-input\"\n                    @keyup.enter.native=\"handleRegister\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 学生专用信息 -->\n          <div v-if=\"registerForm.userType === 'student'\" class=\"form-section\">\n            <h4 class=\"section-title\">学习信息</h4>\n            \n            <div class=\"form-row\">\n              <el-form-item prop=\"grade\" class=\"form-item-half\">\n                <el-select v-model=\"registerForm.grade\" placeholder=\"请选择年级\" class=\"custom-select\">\n                  <el-option label=\"小学一年级\" value=\"小学一年级\"></el-option>\n                  <el-option label=\"小学二年级\" value=\"小学二年级\"></el-option>\n                  <el-option label=\"小学三年级\" value=\"小学三年级\"></el-option>\n                  <el-option label=\"小学四年级\" value=\"小学四年级\"></el-option>\n                  <el-option label=\"小学五年级\" value=\"小学五年级\"></el-option>\n                  <el-option label=\"小学六年级\" value=\"小学六年级\"></el-option>\n                  <el-option label=\"初中一年级\" value=\"初中一年级\"></el-option>\n                  <el-option label=\"初中二年级\" value=\"初中二年级\"></el-option>\n                  <el-option label=\"初中三年级\" value=\"初中三年级\"></el-option>\n                  <el-option label=\"高中一年级\" value=\"高中一年级\"></el-option>\n                  <el-option label=\"高中二年级\" value=\"高中二年级\"></el-option>\n                  <el-option label=\"高中三年级\" value=\"高中三年级\"></el-option>\n                </el-select>\n              </el-form-item>\n\n              <el-form-item prop=\"stage\" class=\"form-item-half\">\n                <el-select v-model=\"registerForm.stage\" placeholder=\"请选择学段\" class=\"custom-select\">\n                  <el-option label=\"小学\" value=\"小学\"></el-option>\n                  <el-option label=\"初中\" value=\"初中\"></el-option>\n                  <el-option label=\"高中\" value=\"高中\"></el-option>\n                </el-select>\n              </el-form-item>\n            </div>\n\n            <div class=\"form-row\">\n              <el-form-item prop=\"mainSubject\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.mainSubject\"\n                    type=\"text\"\n                    placeholder=\"主学科（如：数学）\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"extraSubjects\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.extraSubjects\"\n                    type=\"text\"\n                    placeholder=\"副学科\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 教师专用信息 -->\n          <div v-if=\"registerForm.userType === 'teacher'\" class=\"form-section\">\n            <h4 class=\"section-title\">教学信息</h4>\n            \n            <el-form-item prop=\"certificateNo\">\n              <div class=\"input-group\">\n                <svg-icon icon-class=\"documentation\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"registerForm.certificateNo\"\n                  type=\"text\"\n                  placeholder=\"请输入教师资格证编号\"\n                  class=\"custom-input\"\n                />\n              </div>\n            </el-form-item>\n\n            <div class=\"form-row\">\n              <el-form-item prop=\"stages\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.stages\"\n                    type=\"text\"\n                    placeholder=\"教授学段（如：小学、初中、高中）\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"subjects\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.subjects\"\n                    type=\"text\"\n                    placeholder=\"教授学科（如：数学、语文、英语）\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 验证码 -->\n          <el-form-item prop=\"code\" v-if=\"captchaEnabled\" class=\"captcha-section\">\n            <div class=\"captcha-group\">\n              <div class=\"input-group captcha-input\">\n                <svg-icon icon-class=\"validCode\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"registerForm.code\"\n                  auto-complete=\"off\"\n                  placeholder=\"请输入验证码\"\n                  class=\"custom-input\"\n                  @keyup.enter.native=\"handleRegister\"\n                />\n              </div>\n              <div class=\"captcha-image\" @click=\"getCode\">\n                <img :src=\"codeUrl\" class=\"captcha-img\"/>\n                <div class=\"refresh-tip\">点击刷新</div>\n              </div>\n            </div>\n          </el-form-item>\n\n          <!-- 注册按钮 -->\n          <el-form-item class=\"register-btn-item\">\n            <el-button\n              :loading=\"loading\"\n              type=\"primary\"\n              class=\"register-btn\"\n              @click.native.prevent=\"handleRegister\"\n            >\n              <span v-if=\"!loading\">{{ getRegisterButtonText() }}</span>\n              <span v-else>注册中...</span>\n            </el-button>\n          </el-form-item>\n\n          <div class=\"login-link\">\n            <span>已有账号？</span>\n            <router-link to=\"/login\" class=\"link\">立即登录</router-link>\n          </div>\n        </el-form>\n      </div>\n\n      <!-- 装饰性元素 -->\n      <div class=\"decoration\">\n        <div class=\"circle circle-1\"></div>\n        <div class=\"circle circle-2\"></div>\n        <div class=\"circle circle-3\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg, register } from \"@/api/login\"\n\nexport default {\n  name: \"Register\",\n  data() {\n    const equalToPassword = (rule, value, callback) => {\n      if (this.registerForm.password !== value) {\n        callback(new Error(\"两次输入的密码不一致\"))\n      } else {\n        callback()\n      }\n    }\n    return {\n      title: process.env.VUE_APP_TITLE,\n      codeUrl: \"\",\n      registerForm: {\n        userType: this.$route.query.type || \"student\",\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        phone: \"\",\n        realName: \"\",\n        grade: \"\",\n        stage: \"\",\n        mainSubject: \"\",\n        extraSubjects: \"\",\n        certificateNo: \"\",\n        stages: \"\",\n        subjects: \"\",\n        code: \"\",\n        uuid: \"\"\n      },\n      identityTypes: [\n        { value: \"student\", label: \"学生\", icon: \"education\" },\n        { value: \"parent\", label: \"家长\", icon: \"peoples\" },\n        { value: \"teacher\", label: \"教师\", icon: \"user\" }\n      ],\n      registerRules: {\n        userType: [\n          { required: true, trigger: \"change\", message: \"请选择用户类型\" }\n        ],\n        username: [\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\n          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\n          { min: 5, max: 20, message: \"用户密码长度必须介于 5 和 20 之间\", trigger: \"blur\" },\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\ |\", trigger: \"blur\" }\n        ],\n        confirmPassword: [\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\n        ],\n        phone: [\n          { required: true, trigger: \"blur\", message: \"请输入手机号\" },\n          { pattern: /^1[3-9]\\d{9}$/, message: \"请输入正确的手机号格式\", trigger: \"blur\" }\n        ],\n        realName: [\n          { required: true, trigger: \"blur\", message: \"请输入真实姓名\" }\n        ],\n        grade: [\n          { required: true, trigger: \"change\", message: \"请选择年级\" }\n        ],\n        stage: [\n          { required: true, trigger: \"change\", message: \"请选择学段\" }\n        ],\n        mainSubject: [\n          { required: true, trigger: \"blur\", message: \"请输入主学科\" }\n        ],\n        certificateNo: [\n          { required: true, trigger: \"blur\", message: \"请输入教师资格证编号\" }\n        ],\n        stages: [\n          { required: true, trigger: \"blur\", message: \"请输入教授学段\" }\n        ],\n        subjects: [\n          { required: true, trigger: \"blur\", message: \"请输入教授学科\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      captchaEnabled: true\n    }\n  },\n  created() {\n    this.getCode()\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img\n          this.registerForm.uuid = res.uuid\n        }\n      })\n    },\n    switchIdentity(type) {\n      this.registerForm.userType = type\n      // 清空特定字段\n      this.registerForm.grade = \"\"\n      this.registerForm.stage = \"\"\n      this.registerForm.mainSubject = \"\"\n      this.registerForm.extraSubjects = \"\"\n      this.registerForm.certificateNo = \"\"\n      this.registerForm.stages = \"\"\n      this.registerForm.subjects = \"\"\n      \n      // 清除验证\n      this.$nextTick(() => {\n        this.$refs.registerForm.clearValidate()\n      })\n    },\n    goBack() {\n      this.$router.push('/identity-select')\n    },\n    getIdentityIcon() {\n      const type = this.identityTypes.find(t => t.value === this.registerForm.userType)\n      return type ? type.icon : 'user'\n    },\n    getIdentityTitle() {\n      const type = this.identityTypes.find(t => t.value === this.registerForm.userType)\n      return type ? `${type.label}注册` : '用户注册'\n    },\n    getIdentitySubtitle() {\n      const subtitles = {\n        student: '开启您的学习之旅',\n        parent: '关注孩子的学习成长',\n        teacher: '开启您的教学管理'\n      }\n      return subtitles[this.registerForm.userType] || '欢迎加入'\n    },\n    getRegisterButtonText() {\n      const texts = {\n        student: '开始学习',\n        parent: '开始管理',\n        teacher: '开始教学'\n      }\n      return texts[this.registerForm.userType] || '注册'\n    },\n    handleRegister() {\n      this.$refs.registerForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n          register(this.registerForm).then(res => {\n            const username = this.registerForm.username\n            this.$alert(\"<font color='red'>恭喜你，您的账号 \" + username + \" 注册成功！</font>\", '系统提示', {\n              dangerouslyUseHTMLString: true,\n              type: 'success'\n            }).then(() => {\n              this.$router.push(\"/login\")\n            }).catch(() => {})\n          }).catch(() => {\n            this.loading = false\n            if (this.captchaEnabled) {\n              this.getCode()\n            }\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\n.register {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: url(\"../assets/images/login-background.jpg\") center/cover;\n    opacity: 0.1;\n    z-index: 0;\n  }\n\n  // 添加动态背景粒子效果\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-image: \n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);\n    animation: backgroundShift 8s ease-in-out infinite;\n    z-index: 0;\n  }\n\n  .register-container {\n    position: relative;\n    z-index: 1;\n    width: 100%;\n    max-width: 600px;\n  }\n\n  .back-btn {\n    position: absolute;\n    top: -60px;\n    left: 0;\n    display: flex;\n    align-items: center;\n    color: rgba(255, 255, 255, 0.9);\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n    font-weight: 500;\n\n    &:hover {\n      color: #fff;\n      transform: translateX(-5px);\n    }\n\n    .back-icon {\n      margin-right: 8px;\n      font-size: 16px;\n    }\n  }\n\n  .register-card {\n    background: rgba(255, 255, 255, 0.95);\n    border-radius: 24px;\n    padding: 45px;\n    box-shadow: \n      0 25px 50px rgba(0, 0, 0, 0.15),\n      0 10px 20px rgba(0, 0, 0, 0.1),\n      inset 0 1px 0 rgba(255, 255, 255, 0.6);\n    backdrop-filter: blur(15px);\n    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n    animation: slideInUp 0.8s ease-out;\n    position: relative;\n    overflow: hidden;\n    border: 1px solid rgba(255, 255, 255, 0.2);\n\n    // 光泽效果\n    &::before {\n      content: '';\n      position: absolute;\n      top: -50%;\n      left: -50%;\n      width: 200%;\n      height: 200%;\n      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);\n      transform: rotate(45deg);\n      transition: all 0.6s ease;\n      opacity: 0;\n      pointer-events: none;\n      z-index: 1;\n    }\n\n    // 边框光效\n    &::after {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      border-radius: 24px;\n      padding: 2px;\n      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n      mask-composite: exclude;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n      pointer-events: none;\n      z-index: 1;\n    }\n\n    &:hover {\n      transform: translateY(-8px) scale(1.02);\n      box-shadow: \n        0 35px 70px rgba(0, 0, 0, 0.25),\n        0 15px 30px rgba(0, 0, 0, 0.15),\n        inset 0 1px 0 rgba(255, 255, 255, 0.8);\n\n      &::before {\n        opacity: 1;\n        transform: rotate(45deg) translate(50%, 50%);\n      }\n\n      &::after {\n        opacity: 1;\n      }\n    }\n\n    .card-header {\n      text-align: center;\n      margin-bottom: 30px;\n\n      .identity-icon {\n        width: 80px;\n        height: 80px;\n        margin: 0 auto 20px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.3s ease;\n\n        .icon {\n          font-size: 2.5rem;\n        }\n      }\n\n      .card-title {\n        font-size: 1.8rem;\n        font-weight: 600;\n        margin: 0 0 10px 0;\n        color: #333;\n      }\n\n      .card-subtitle {\n        font-size: 1rem;\n        color: #666;\n        margin: 0;\n      }\n    }\n\n    .identity-switch {\n      display: flex;\n      background: rgba(245, 245, 245, 0.8);\n      border-radius: 16px;\n      padding: 6px;\n      margin-bottom: 35px;\n      backdrop-filter: blur(5px);\n      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);\n\n      .switch-item {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 14px 10px;\n        border-radius: 12px;\n        cursor: pointer;\n        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n        font-size: 15px;\n        font-weight: 600;\n        position: relative;\n        overflow: hidden;\n\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));\n          border-radius: 12px;\n          opacity: 0;\n          transition: opacity 0.3s ease;\n          pointer-events: none;\n        }\n\n        .switch-icon {\n          margin-right: 8px;\n          font-size: 18px;\n          transition: all 0.3s ease;\n        }\n\n        &.active {\n          background: #fff;\n          color: #409EFF;\n          box-shadow: \n            0 4px 12px rgba(0, 0, 0, 0.15),\n            0 2px 4px rgba(64, 158, 255, 0.2);\n          transform: translateY(-2px);\n\n          &::before {\n            opacity: 1;\n          }\n\n          .switch-icon {\n            transform: scale(1.1);\n          }\n        }\n\n        &:hover:not(.active) {\n          background: rgba(255, 255, 255, 0.9);\n          transform: translateY(-1px);\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n        }\n      }\n    }\n\n    .register-form {\n      position: relative;\n      z-index: 2;\n      \n      .form-section {\n        margin-bottom: 30px;\n\n        .section-title {\n          font-size: 1.1rem;\n          font-weight: 600;\n          color: #333;\n          margin: 0 0 20px 0;\n          padding-bottom: 10px;\n          border-bottom: 2px solid #f0f0f0;\n        }\n\n        .form-row {\n          display: flex;\n          gap: 15px;\n          margin-bottom: 20px;\n\n          .form-item-half {\n            flex: 1;\n            margin-bottom: 0;\n          }\n        }\n\n        .input-group {\n          position: relative;\n          margin-bottom: 25px;\n\n          .input-icon {\n            position: absolute;\n            left: 20px;\n            top: 50%;\n            transform: translateY(-50%);\n            color: #999;\n            font-size: 16px;\n            z-index: 3;\n            transition: all 0.3s ease;\n          }\n\n          .custom-input {\n            ::v-deep .el-input__inner {\n              height: 55px !important;\n              padding-left: 60px !important;\n              border: 2px solid #e8e8e8 !important;\n              border-radius: 16px !important;\n              font-size: 16px !important;\n              transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;\n              background: rgba(255, 255, 255, 0.9) !important;\n              backdrop-filter: blur(5px);\n              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;\n\n              &:focus {\n                border-color: #409EFF !important;\n                box-shadow: \n                  0 0 0 4px rgba(64, 158, 255, 0.15),\n                  0 4px 12px rgba(64, 158, 255, 0.1) !important;\n                background: rgba(255, 255, 255, 1) !important;\n                transform: translateY(-2px) !important;\n              }\n\n              &:hover {\n                border-color: #c0c4cc !important;\n                transform: translateY(-1px) !important;\n                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;\n              }\n            }\n          }\n\n          &:hover .input-icon {\n            color: #666;\n            transform: translateY(-50%) scale(1.1);\n          }\n        }\n\n        .custom-select {\n          width: 100%;\n\n          .el-input__inner {\n            height: 55px;\n            border: 2px solid #e8e8e8;\n            border-radius: 16px;\n            font-size: 16px;\n            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n            background: rgba(255, 255, 255, 0.9);\n            backdrop-filter: blur(5px);\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n\n            &:focus {\n              border-color: #409EFF;\n              box-shadow: \n                0 0 0 4px rgba(64, 158, 255, 0.15),\n                0 4px 12px rgba(64, 158, 255, 0.1);\n              background: rgba(255, 255, 255, 1);\n              transform: translateY(-2px);\n            }\n\n            &:hover {\n              border-color: #c0c4cc;\n              transform: translateY(-1px);\n              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n            }\n          }\n        }\n      }\n\n      .captcha-section {\n        margin-bottom: 30px;\n\n        .captcha-group {\n          display: flex;\n          align-items: center;\n          gap: 15px;\n\n          .captcha-input {\n            flex: 1;\n            margin-bottom: 0;\n          }\n\n          .captcha-image {\n            width: 120px;\n            height: 55px;\n            border-radius: 16px;\n            overflow: hidden;\n            cursor: pointer;\n            position: relative;\n            border: 2px solid #e8e8e8;\n            transition: all 0.3s ease;\n            background: rgba(255, 255, 255, 0.9);\n            backdrop-filter: blur(5px);\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n\n            &:hover {\n              border-color: #409EFF;\n              transform: scale(1.02);\n              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n            }\n\n            .captcha-img {\n              width: 100%;\n              height: 100%;\n              object-fit: cover;\n            }\n\n            .refresh-tip {\n              position: absolute;\n              bottom: 0;\n              left: 0;\n              right: 0;\n              background: rgba(0, 0, 0, 0.7);\n              color: #fff;\n              font-size: 12px;\n              text-align: center;\n              padding: 2px;\n              opacity: 0;\n              transition: opacity 0.3s ease;\n            }\n\n            &:hover .refresh-tip {\n              opacity: 1;\n            }\n          }\n        }\n      }\n\n      .register-btn-item {\n        margin-bottom: 25px;\n\n        .register-btn {\n          width: 100%;\n          height: 55px;\n          border-radius: 16px;\n          font-size: 17px;\n          font-weight: 700;\n          transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n          position: relative;\n          overflow: hidden;\n          border: none;\n          background: linear-gradient(135deg, #409EFF, #66b1ff);\n          box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);\n\n          &::before {\n            content: '';\n            position: absolute;\n            top: 0;\n            left: -100%;\n            width: 100%;\n            height: 100%;\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n            transition: left 0.5s ease;\n          }\n\n          &:hover {\n            transform: translateY(-3px) scale(1.02);\n            box-shadow: 0 12px 30px rgba(64, 158, 255, 0.4);\n            background: linear-gradient(135deg, #66b1ff, #409EFF);\n\n            &::before {\n              left: 100%;\n            }\n          }\n\n          &:active {\n            transform: translateY(-1px) scale(0.98);\n          }\n        }\n      }\n\n      .login-link {\n        text-align: center;\n        color: #666;\n        font-size: 14px;\n\n        .link {\n          color: #409EFF;\n          text-decoration: none;\n          font-weight: 500;\n          margin-left: 5px;\n          transition: color 0.3s ease;\n\n          &:hover {\n            color: #66b1ff;\n          }\n        }\n      }\n    }\n\n    // 主题样式\n    &.student-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #409EFF, #66b1ff);\n        color: #fff;\n      }\n    }\n\n    &.parent-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #67C23A, #85ce61);\n        color: #fff;\n      }\n    }\n\n    &.teacher-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #E6A23C, #f0c78a);\n        color: #fff;\n      }\n    }\n  }\n\n  .decoration {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    pointer-events: none;\n    z-index: 0;\n\n    .circle {\n      position: absolute;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      animation: float 6s ease-in-out infinite;\n\n      &.circle-1 {\n        width: 100px;\n        height: 100px;\n        top: 20%;\n        left: 10%;\n        animation-delay: 0s;\n      }\n\n      &.circle-2 {\n        width: 150px;\n        height: 150px;\n        top: 60%;\n        right: 10%;\n        animation-delay: 2s;\n      }\n\n      &.circle-3 {\n        width: 80px;\n        height: 80px;\n        bottom: 20%;\n        left: 20%;\n        animation-delay: 4s;\n      }\n    }\n  }\n}\n\n// 动画定义\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n@keyframes backgroundShift {\n  0%, 100% {\n    transform: translateX(0) translateY(0);\n  }\n  25% {\n    transform: translateX(-15px) translateY(-8px);\n  }\n  50% {\n    transform: translateX(15px) translateY(8px);\n  }\n  75% {\n    transform: translateX(-8px) translateY(15px);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n@keyframes shimmer {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .register {\n    padding: 10px;\n\n    .register-container {\n      max-width: 100%;\n    }\n\n    .register-card {\n      padding: 30px 20px;\n      margin-top: 40px;\n\n      .card-header .identity-icon {\n        width: 60px;\n        height: 60px;\n\n        .icon {\n          font-size: 2rem;\n        }\n      }\n\n      .identity-switch {\n        .switch-item {\n          padding: 10px 4px;\n          font-size: 12px;\n\n          .switch-icon {\n            font-size: 14px;\n          }\n        }\n      }\n\n      .register-form {\n        .form-section {\n          .form-row {\n            flex-direction: column;\n            gap: 0;\n\n            .form-item-half {\n              margin-bottom: 20px;\n            }\n          }\n        }\n      }\n    }\n\n    .back-btn {\n      top: -50px;\n    }\n  }\n}\n</style>\n"]}]}