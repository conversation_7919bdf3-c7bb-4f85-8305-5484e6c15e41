package com.lingdu.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lingdu.common.annotation.Excel;
import com.lingdu.common.core.domain.BaseEntity;

/**
 * 教师信息对象 teacher_info
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
public class TeacherInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 教师ID */
    private Long teacherId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 电话 */
    @Excel(name = "电话")
    private String phone;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 教师资格证编号 */
    @Excel(name = "教师资格证编号")
    private String certificateNo;

    /** 审核状态 */
    @Excel(name = "审核状态")
    private Integer verifyStatus;

    /** 教授学段 */
    @Excel(name = "教授学段")
    private String stages;

    /** 教授学科 */
    @Excel(name = "教授学科")
    private String subjects;

    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setCertificateNo(String certificateNo) 
    {
        this.certificateNo = certificateNo;
    }

    public String getCertificateNo() 
    {
        return certificateNo;
    }

    public void setVerifyStatus(Integer verifyStatus) 
    {
        this.verifyStatus = verifyStatus;
    }

    public Integer getVerifyStatus() 
    {
        return verifyStatus;
    }

    public void setStages(String stages) 
    {
        this.stages = stages;
    }

    public String getStages() 
    {
        return stages;
    }

    public void setSubjects(String subjects) 
    {
        this.subjects = subjects;
    }

    public String getSubjects() 
    {
        return subjects;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("teacherId", getTeacherId())
            .append("userId", getUserId())
            .append("phone", getPhone())
            .append("name", getName())
            .append("certificateNo", getCertificateNo())
            .append("verifyStatus", getVerifyStatus())
            .append("stages", getStages())
            .append("subjects", getSubjects())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
