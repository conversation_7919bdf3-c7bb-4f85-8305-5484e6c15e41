package com.lingdu.system.service.impl;

import java.util.List;
import com.lingdu.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lingdu.system.mapper.studentInfoMapper;
import com.lingdu.system.domain.studentInfo;
import com.lingdu.system.service.IstudentInfoService;

/**
 * 学生信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
@Service
public class studentInfoServiceImpl implements IstudentInfoService 
{
    @Autowired
    private studentInfoMapper studentInfoMapper;

    /**
     * 查询学生信息
     * 
     * @param studentId 学生信息主键
     * @return 学生信息
     */
    @Override
    public studentInfo selectstudentInfoByStudentId(Long studentId)
    {
        return studentInfoMapper.selectstudentInfoByStudentId(studentId);
    }

    /**
     * 查询学生信息列表
     * 
     * @param studentInfo 学生信息
     * @return 学生信息
     */
    @Override
    public List<studentInfo> selectstudentInfoList(studentInfo studentInfo)
    {
        return studentInfoMapper.selectstudentInfoList(studentInfo);
    }

    /**
     * 新增学生信息
     * 
     * @param studentInfo 学生信息
     * @return 结果
     */
    @Override
    public int insertstudentInfo(studentInfo studentInfo)
    {
        studentInfo.setCreateTime(DateUtils.getNowDate());
        return studentInfoMapper.insertstudentInfo(studentInfo);
    }

    /**
     * 修改学生信息
     * 
     * @param studentInfo 学生信息
     * @return 结果
     */
    @Override
    public int updatestudentInfo(studentInfo studentInfo)
    {
        studentInfo.setUpdateTime(DateUtils.getNowDate());
        return studentInfoMapper.updatestudentInfo(studentInfo);
    }

    /**
     * 批量删除学生信息
     * 
     * @param studentIds 需要删除的学生信息主键
     * @return 结果
     */
    @Override
    public int deletestudentInfoByStudentIds(Long[] studentIds)
    {
        return studentInfoMapper.deletestudentInfoByStudentIds(studentIds);
    }

    /**
     * 删除学生信息信息
     * 
     * @param studentId 学生信息主键
     * @return 结果
     */
    @Override
    public int deletestudentInfoByStudentId(Long studentId)
    {
        return studentInfoMapper.deletestudentInfoByStudentId(studentId);
    }
}
