import request from '@/utils/request'

// 查询家长信息列表
export function listParentinfo(query) {
  return request({
    url: '/user/parentinfo/list',
    method: 'get',
    params: query
  })
}

// 查询家长信息详细
export function getParentinfo(parentId) {
  return request({
    url: '/user/parentinfo/' + parentId,
    method: 'get'
  })
}

// 新增家长信息
export function addParentinfo(data) {
  return request({
    url: '/user/parentinfo',
    method: 'post',
    data: data
  })
}

// 修改家长信息
export function updateParentinfo(data) {
  return request({
    url: '/user/parentinfo',
    method: 'put',
    data: data
  })
}

// 删除家长信息
export function delParentinfo(parentId) {
  return request({
    url: '/user/parentinfo/' + parentId,
    method: 'delete'
  })
}
