<template>
    <div class="ai-learning-page">
        <el-header class="header" height="64px">
            <el-row justify="center">
                <el-col :span="24">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="el-icon-reading"></i>
                            选择学习科目
                        </h2>
                        <p class="section-subtitle">选择您想要学习的科目，开始您的AI学习之旅</p>
                    </div>
                </el-col>
            </el-row>
        </el-header>
        <!-- 主要内容区域 -->
        <el-main class="main-content">
            <el-container class="content-container">


                <!-- 学科卡片网格 -->
                <el-row :gutter="24" class="subjects-row">
                    <el-col :xs="12" :sm="8" :md="8" :lg="8" :xl="8" v-for="(subject, index) in subjects" :key="index"
                        class="subject-col">
                        <el-card :body-style="{ padding: '32px 20px' }" class="subject-card" shadow="hover"
                            @click.native="goToStudy(subject)">
                            <div class="subject-content">
                                <div class="subject-icon">
                                    <i :class="subject.icon"></i>
                                </div>
                                <div class="subject-name">{{ subject.name }}</div>
                                <div class="subject-description">{{ subject.description }}</div>
                                <el-button type="text" class="study-btn">
                                    开始学习
                                </el-button>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </el-container>
        </el-main>

        <site-footer />
    </div>
</template>

<script>
import SiteFooter from '@/components/SiteFooter'
export default {
    name: 'AILearningPage',
    components: { SiteFooter },
    data() {
        return {
            activeIndex: 'home',
            loginLoading: false,
            registerLoading: false,
            subjects: [
                {
                    name: '小学数学',
                    icon: 'el-icon-reading',
                    description: '启蒙阶段，重视兴趣培养与基础能力：识字、拼音、四则运算、科学常识等。课程注重体验式学习和良好习惯形成。',
                    link: '/agent',
                    color: '#409EFF'
                },
                {
                    name: '初中数学',
                    icon: 'el-icon-reading',
                    description: '启蒙阶段，重视兴趣培养与基础能力：识字、拼音、四则运算、科学常识等。课程注重体验式学习和良好习惯形成。',
                    link: '/agent',
                    color: '#409EFF'
                },
                {
                    name: '高中数学',
                    icon: 'el-icon-reading',
                    description: '系统夯实学科基础：语数英全面提升，物化生引入概念与实验探究，历史地理形成时空观念与区域认知。',
                    link: '/agent',
                    color: '#67C23A'
                },
                {
                    name: '小学英语',
                    icon: 'el-icon-s-management',
                    description: '系统夯实学科基础：语数英全面提升，物化生引入概念与实验探究，历史地理形成时空观念与区域认知。',
                    link: '/agent',
                    color: '#67C23A'
                },
                {
                    name: '初中英语',
                    icon: 'el-icon-s-management',
                    description: '学科深化与思维拔高：函数与几何、物理建模、化学反应机理、生物遗传与生态、文学鉴赏与写作、英语高阶应用。',
                    link: '/agent',
                    color: '#E6A23C'
                },
                {
                    name: '高中英语',
                    icon: 'el-icon-s-management',
                    description: '学科深化与思维拔高：函数与几何、物理建模、化学反应机理、生物遗传与生态、文学鉴赏与写作、英语高阶应用。',
                    link: '/agent',
                    color: '#E6A23C'
                },
            ]
        }
    },
    methods: {
        goToStudy(subject) {
            this.$message({
                message: `正在进入${subject.name}学习页面...`,
                type: 'success',
                duration: 1500
            });

            // 模拟加载延迟
            setTimeout(() => {
                this.$router.push({
                    path: subject.link,
                    query: { subject: subject.name }
                });
            }, 500);
        },

        handleLogin() {
            this.loginLoading = true;
            setTimeout(() => {
                this.loginLoading = false;
                this.$router.push('/login');
            }, 1000);
        },

        handleRegister() {
            this.registerLoading = true;
            setTimeout(() => {
                this.registerLoading = false;
                this.$router.push('/register');
            }, 1000);
        },

        handleSelect(key) {
            this.activeIndex = key;
            switch (key) {
                case 'home':
                    this.$message('您已在首页');
                    break;
                case 'about':
                    this.$message('关于我们页面开发中...');
                    break;
                case 'help':
                    this.$message('帮助页面开发中...');
                    break;
            }
        }
    },

    mounted() {
        // 页面加载完成后的初始化
        this.$nextTick(() => {
            this.$message({
                message: '欢迎使用灵渡AI学习助手！',
                type: 'success',
                duration: 3000
            });
        });
    }
}
</script>

<style scoped>
.ai-learning-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    margin-top: 20px;
}

.header-row {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-col {
    display: flex;
    justify-content: center;
}

.nav-menu {
    border-bottom: none;
    background: transparent;
}

.nav-menu .el-menu-item {
    color: #606266;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.nav-menu .el-menu-item:hover,
.nav-menu .el-menu-item.is-active {
    color: #409EFF;
    border-bottom-color: #409EFF;
    background: transparent;
}

.auth-col {
    display: flex;
    justify-content: flex-end;
}

.auth-buttons {
    display: flex;
    gap: 12px;
}

.login-btn {
    border: 1px solid #409EFF;
    color: #409EFF;
    background: transparent;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.login-btn:hover {
    background-color: #409EFF;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 主要内容区域 */
.main-content {
    padding: 60px 0;
    background: transparent;
    flex: 1;
}

.content-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 60px 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 36px;
    font-weight: 700;
    color: #303133;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.section-title i {
    color: #409EFF;
    font-size: 32px;
}

.section-subtitle {
    font-size: 16px;
    color: #909399;
    margin: 0;
    font-weight: 400;
}

/* 学科卡片网格 */
.subjects-row {
    margin-top: 5px;
}

.subject-col {
    margin-bottom: 24px;
}

.subject-card {
    border-radius: 16px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    height: 250px;
}

/* 让卡片内容填满高度并垂直居中，保证每张卡片视觉高度一致 */
.subject-card .el-card__body {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.subject-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.subject-card:hover::before {
    transform: scaleX(1);
}

.subject-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
    border-color: #409EFF;
}

.subject-content {
    text-align: center;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0px;
}

.subject-icon {
    margin-bottom: 20px;
}

.subject-icon i {
    font-size: 48px;
    color: #409EFF;
    display: block;
    transition: all 0.3s ease;
}

.subject-card:hover .subject-icon i {
    transform: scale(1.1);
    color: #667eea;
}

.subject-name {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
}

.subject-description {
    font-size: 14px;
    color: #909399;
    margin-bottom: 20px;
    line-height: 1.5;
    text-align: center;
    max-width: 90%;
    display: -webkit-box;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.study-btn {
    color: #409EFF;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.study-btn:hover {
    background-color: #ecf5ff;
    color: #409EFF;
}

/* 底部样式 */
.footer {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-top: 1px solid #e4e7ed;
    margin-top: auto;
}

.footer-content {
    text-align: center;
}

.footer-content p {
    color: #909399;
    font-size: 14px;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-row {
        padding: 0 16px;
    }

    .title {
        font-size: 18px;
    }

    .nav-col {
        display: none;
    }

    .auth-col {
        justify-content: center;
    }

    .auth-buttons {
        gap: 8px;
    }

    .section-title {
        font-size: 28px;
        flex-direction: column;
        gap: 8px;
    }

    .section-subtitle {
        font-size: 14px;
    }

    .subjects-row {
        margin-top: 30px;
    }

    .subject-card {
        margin-bottom: 16px;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 40px 0;
    }

    .section-title {
        font-size: 24px;
    }

    .subject-icon i {
        font-size: 36px;
    }

    .subject-name {
        font-size: 18px;
    }
}

/* Element UI 组件样式覆盖 */
.el-header {
    padding: 0;
}

.el-main {
    padding: 0;
}

.el-footer {
    padding: 0;
}

.el-card {
    border: none;
}

.el-card:hover {
    border-color: #409EFF;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.subject-card {
    animation: fadeInUp 0.6s ease-out;
}

.subject-card:nth-child(1) {
    animation-delay: 0.1s;
}

.subject-card:nth-child(2) {
    animation-delay: 0.2s;
}

.subject-card:nth-child(3) {
    animation-delay: 0.3s;
}

.subject-card:nth-child(4) {
    animation-delay: 0.4s;
}

.subject-card:nth-child(5) {
    animation-delay: 0.5s;
}

.subject-card:nth-child(6) {
    animation-delay: 0.6s;
}

.subject-card:nth-child(7) {
    animation-delay: 0.7s;
}

.subject-card:nth-child(8) {
    animation-delay: 0.8s;
}
</style>