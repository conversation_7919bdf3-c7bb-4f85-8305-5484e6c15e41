<template>
  <div class="register">
    <div class="register-container">
      <!-- 返回按钮 -->
      <div class="back-btn" @click="goBack">
        <svg-icon icon-class="arrow-left" class="back-icon" />
        <span>返回选择</span>
      </div>

      <!-- 注册卡片 -->
      <div class="register-card" :class="`${registerForm.userType}-theme`">
        <div class="card-header">
          <div class="identity-icon">
            <svg-icon :icon-class="getIdentityIcon()" class="icon" />
          </div>
          <h2 class="card-title">{{ getIdentityTitle() }}</h2>
          <p class="card-subtitle">{{ getIdentitySubtitle() }}</p>
        </div>

        <el-form ref="registerForm" :model="registerForm" :rules="registerRules" class="register-form">
          <!-- 身份切换 -->
          <div class="identity-switch">
            <div 
              v-for="type in identityTypes" 
              :key="type.value"
              class="switch-item"
              :class="{ active: registerForm.userType === type.value }"
              @click="switchIdentity(type.value)"
            >
              <svg-icon :icon-class="type.icon" class="switch-icon" />
              <span>{{ type.label }}</span>
            </div>
          </div>

          <!-- 基本信息 -->
          <div class="form-section">
            <h4 class="section-title">基本信息</h4>
            
            <div class="form-row">
              <el-form-item prop="username" class="form-item-half">
                <div class="input-group">
                  <svg-icon icon-class="user" class="input-icon" />
                  <el-input
                    v-model="registerForm.username"
                    type="text"
                    auto-complete="off"
                    placeholder="请输入账号"
                    class="custom-input"
                  />
                </div>
              </el-form-item>

              <el-form-item prop="phone" class="form-item-half">
                <div class="input-group">
                  <svg-icon icon-class="phone" class="input-icon" />
                  <el-input
                    v-model="registerForm.phone"
                    type="text"
                    auto-complete="off"
                    placeholder="请输入手机号"
                    class="custom-input"
                  />
                </div>
              </el-form-item>
            </div>

            <el-form-item prop="realName">
              <div class="input-group">
                <svg-icon icon-class="user" class="input-icon" />
                <el-input
                  v-model="registerForm.realName"
                  type="text"
                  auto-complete="off"
                  placeholder="请输入真实姓名"
                  class="custom-input"
                />
              </div>
            </el-form-item>

            <div class="form-row">
              <el-form-item prop="password" class="form-item-half">
                <div class="input-group">
                  <svg-icon icon-class="password" class="input-icon" />
                  <el-input
                    v-model="registerForm.password"
                    type="password"
                    auto-complete="off"
                    placeholder="请输入密码"
                    class="custom-input"
                    @keyup.enter.native="handleRegister"
                  />
                </div>
              </el-form-item>

              <el-form-item prop="confirmPassword" class="form-item-half">
                <div class="input-group">
                  <svg-icon icon-class="password" class="input-icon" />
                  <el-input
                    v-model="registerForm.confirmPassword"
                    type="password"
                    auto-complete="off"
                    placeholder="确认密码"
                    class="custom-input"
                    @keyup.enter.native="handleRegister"
                  />
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- 学生专用信息 -->
          <div v-if="registerForm.userType === 'student'" class="form-section">
            <h4 class="section-title">学习信息</h4>
            
            <div class="form-row">
              <el-form-item prop="grade" class="form-item-half">
                <el-select v-model="registerForm.grade" placeholder="请选择年级" class="custom-select">
                  <el-option label="小学一年级" value="小学一年级"></el-option>
                  <el-option label="小学二年级" value="小学二年级"></el-option>
                  <el-option label="小学三年级" value="小学三年级"></el-option>
                  <el-option label="小学四年级" value="小学四年级"></el-option>
                  <el-option label="小学五年级" value="小学五年级"></el-option>
                  <el-option label="小学六年级" value="小学六年级"></el-option>
                  <el-option label="初中一年级" value="初中一年级"></el-option>
                  <el-option label="初中二年级" value="初中二年级"></el-option>
                  <el-option label="初中三年级" value="初中三年级"></el-option>
                  <el-option label="高中一年级" value="高中一年级"></el-option>
                  <el-option label="高中二年级" value="高中二年级"></el-option>
                  <el-option label="高中三年级" value="高中三年级"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item prop="stage" class="form-item-half">
                <el-select v-model="registerForm.stage" placeholder="请选择学段" class="custom-select">
                  <el-option label="小学" value="小学"></el-option>
                  <el-option label="初中" value="初中"></el-option>
                  <el-option label="高中" value="高中"></el-option>
                </el-select>
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item prop="mainSubject" class="form-item-half">
                <div class="input-group">
                  <svg-icon icon-class="education" class="input-icon" />
                  <el-input
                    v-model="registerForm.mainSubject"
                    type="text"
                    placeholder="主学科（如：数学）"
                    class="custom-input"
                  />
                </div>
              </el-form-item>

              <el-form-item prop="extraSubjects" class="form-item-half">
                <div class="input-group">
                  <svg-icon icon-class="education" class="input-icon" />
                  <el-input
                    v-model="registerForm.extraSubjects"
                    type="text"
                    placeholder="副学科"
                    class="custom-input"
                  />
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- 教师专用信息 -->
          <div v-if="registerForm.userType === 'teacher'" class="form-section">
            <h4 class="section-title">教学信息</h4>
            
            <el-form-item prop="certificateNo">
              <div class="input-group">
                <svg-icon icon-class="documentation" class="input-icon" />
                <el-input
                  v-model="registerForm.certificateNo"
                  type="text"
                  placeholder="请输入教师资格证编号"
                  class="custom-input"
                />
              </div>
            </el-form-item>

            <div class="form-row">
              <el-form-item prop="stages" class="form-item-half">
                <div class="input-group">
                  <svg-icon icon-class="education" class="input-icon" />
                  <el-input
                    v-model="registerForm.stages"
                    type="text"
                    placeholder="教授学段（如：小学、初中、高中）"
                    class="custom-input"
                  />
                </div>
              </el-form-item>

              <el-form-item prop="subjects" class="form-item-half">
                <div class="input-group">
                  <svg-icon icon-class="education" class="input-icon" />
                  <el-input
                    v-model="registerForm.subjects"
                    type="text"
                    placeholder="教授学科（如：数学、语文、英语）"
                    class="custom-input"
                  />
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- 验证码 -->
          <el-form-item prop="code" v-if="captchaEnabled" class="captcha-section">
            <div class="captcha-group">
              <div class="input-group captcha-input">
                <svg-icon icon-class="validCode" class="input-icon" />
                <el-input
                  v-model="registerForm.code"
                  auto-complete="off"
                  placeholder="请输入验证码"
                  class="custom-input"
                  @keyup.enter.native="handleRegister"
                />
              </div>
              <div class="captcha-image" @click="getCode">
                <img :src="codeUrl" class="captcha-img"/>
                <div class="refresh-tip">点击刷新</div>
              </div>
            </div>
          </el-form-item>

          <!-- 注册按钮 -->
          <el-form-item class="register-btn-item">
            <el-button
              :loading="loading"
              type="primary"
              class="register-btn"
              @click.native.prevent="handleRegister"
            >
              <span v-if="!loading">{{ getRegisterButtonText() }}</span>
              <span v-else>注册中...</span>
            </el-button>
          </el-form-item>

          <div class="login-link">
            <span>已有账号？</span>
            <router-link to="/login" class="link">立即登录</router-link>
          </div>
        </el-form>
      </div>

      <!-- 装饰性元素 -->
      <div class="decoration">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCodeImg, register } from "@/api/login"

export default {
  name: "Register",
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.registerForm.password !== value) {
        callback(new Error("两次输入的密码不一致"))
      } else {
        callback()
      }
    }
    return {
      title: process.env.VUE_APP_TITLE,
      codeUrl: "",
      registerForm: {
        userType: this.$route.query.type || "student",
        username: "",
        password: "",
        confirmPassword: "",
        phone: "",
        realName: "",
        grade: "",
        stage: "",
        mainSubject: "",
        extraSubjects: "",
        certificateNo: "",
        stages: "",
        subjects: "",
        code: "",
        uuid: ""
      },
      identityTypes: [
        { value: "student", label: "学生", icon: "education" },
        { value: "parent", label: "家长", icon: "peoples" },
        { value: "teacher", label: "教师", icon: "user" }
      ],
      registerRules: {
        userType: [
          { required: true, trigger: "change", message: "请选择用户类型" }
        ],
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" },
          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
          { min: 5, max: 20, message: "用户密码长度必须介于 5 和 20 之间", trigger: "blur" },
          { pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\ |", trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, trigger: "blur", message: "请再次输入您的密码" },
          { required: true, validator: equalToPassword, trigger: "blur" }
        ],
        phone: [
          { required: true, trigger: "blur", message: "请输入手机号" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号格式", trigger: "blur" }
        ],
        realName: [
          { required: true, trigger: "blur", message: "请输入真实姓名" }
        ],
        grade: [
          { required: true, trigger: "change", message: "请选择年级" }
        ],
        stage: [
          { required: true, trigger: "change", message: "请选择学段" }
        ],
        mainSubject: [
          { required: true, trigger: "blur", message: "请输入主学科" }
        ],
        certificateNo: [
          { required: true, trigger: "blur", message: "请输入教师资格证编号" }
        ],
        stages: [
          { required: true, trigger: "blur", message: "请输入教授学段" }
        ],
        subjects: [
          { required: true, trigger: "blur", message: "请输入教授学科" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      captchaEnabled: true
    }
  },
  created() {
    this.getCode()
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img
          this.registerForm.uuid = res.uuid
        }
      })
    },
    switchIdentity(type) {
      this.registerForm.userType = type
      // 清空特定字段
      this.registerForm.grade = ""
      this.registerForm.stage = ""
      this.registerForm.mainSubject = ""
      this.registerForm.extraSubjects = ""
      this.registerForm.certificateNo = ""
      this.registerForm.stages = ""
      this.registerForm.subjects = ""
      
      // 清除验证
      this.$nextTick(() => {
        this.$refs.registerForm.clearValidate()
      })
    },
    goBack() {
      this.$router.push('/identity-select')
    },
    getIdentityIcon() {
      const type = this.identityTypes.find(t => t.value === this.registerForm.userType)
      return type ? type.icon : 'user'
    },
    getIdentityTitle() {
      const type = this.identityTypes.find(t => t.value === this.registerForm.userType)
      return type ? `${type.label}注册` : '用户注册'
    },
    getIdentitySubtitle() {
      const subtitles = {
        student: '开启您的学习之旅',
        parent: '关注孩子的学习成长',
        teacher: '开启您的教学管理'
      }
      return subtitles[this.registerForm.userType] || '欢迎加入'
    },
    getRegisterButtonText() {
      const texts = {
        student: '开始学习',
        parent: '开始管理',
        teacher: '开始教学'
      }
      return texts[this.registerForm.userType] || '注册'
    },
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          this.loading = true
          register(this.registerForm).then(res => {
            const username = this.registerForm.username
            this.$alert("<font color='red'>恭喜你，您的账号 " + username + " 注册成功！</font>", '系统提示', {
              dangerouslyUseHTMLString: true,
              type: 'success'
            }).then(() => {
              this.$router.push("/login")
            }).catch(() => {})
          }).catch(() => {
            this.loading = false
            if (this.captchaEnabled) {
              this.getCode()
            }
          })
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.register {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("../assets/images/login-background.jpg") center/cover;
    opacity: 0.1;
    z-index: 0;
  }

  // 添加动态背景粒子效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundShift 8s ease-in-out infinite;
    z-index: 0;
  }

  .register-container {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: 600px;
  }

  .back-btn {
    position: absolute;
    top: -60px;
    left: 0;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;

    &:hover {
      color: #fff;
      transform: translateX(-5px);
    }

    .back-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }

  .register-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    padding: 45px;
    box-shadow: 
      0 25px 50px rgba(0, 0, 0, 0.15),
      0 10px 20px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(15px);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    animation: slideInUp 0.8s ease-out;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);

    // 光泽效果
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transform: rotate(45deg);
      transition: all 0.6s ease;
      opacity: 0;
      pointer-events: none;
      z-index: 1;
    }

    // 边框光效
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 24px;
      padding: 2px;
      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: exclude;
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
      z-index: 1;
    }

    &:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 
        0 35px 70px rgba(0, 0, 0, 0.25),
        0 15px 30px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);

      &::before {
        opacity: 1;
        transform: rotate(45deg) translate(50%, 50%);
      }

      &::after {
        opacity: 1;
      }
    }

    .card-header {
      text-align: center;
      margin-bottom: 30px;

      .identity-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        .icon {
          font-size: 2.5rem;
        }
      }

      .card-title {
        font-size: 1.8rem;
        font-weight: 600;
        margin: 0 0 10px 0;
        color: #333;
      }

      .card-subtitle {
        font-size: 1rem;
        color: #666;
        margin: 0;
      }
    }

    .identity-switch {
      display: flex;
      background: rgba(245, 245, 245, 0.8);
      border-radius: 16px;
      padding: 6px;
      margin-bottom: 35px;
      backdrop-filter: blur(5px);
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);

      .switch-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 14px 10px;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        font-size: 15px;
        font-weight: 600;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));
          border-radius: 12px;
          opacity: 0;
          transition: opacity 0.3s ease;
          pointer-events: none;
        }

        .switch-icon {
          margin-right: 8px;
          font-size: 18px;
          transition: all 0.3s ease;
        }

        &.active {
          background: #fff;
          color: #409EFF;
          box-shadow: 
            0 4px 12px rgba(0, 0, 0, 0.15),
            0 2px 4px rgba(64, 158, 255, 0.2);
          transform: translateY(-2px);

          &::before {
            opacity: 1;
          }

          .switch-icon {
            transform: scale(1.1);
          }
        }

        &:hover:not(.active) {
          background: rgba(255, 255, 255, 0.9);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .register-form {
      position: relative;
      z-index: 2;
      
      .form-section {
        margin-bottom: 30px;

        .section-title {
          font-size: 1.1rem;
          font-weight: 600;
          color: #333;
          margin: 0 0 20px 0;
          padding-bottom: 10px;
          border-bottom: 2px solid #f0f0f0;
        }

        .form-row {
          display: flex;
          gap: 15px;
          margin-bottom: 20px;

          .form-item-half {
            flex: 1;
            margin-bottom: 0;
          }
        }

        .input-group {
          position: relative;
          margin-bottom: 25px;

          .input-icon {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 16px;
            z-index: 3;
            transition: all 0.3s ease;
          }

          .custom-input {
            ::v-deep .el-input__inner {
              height: 55px !important;
              padding-left: 60px !important;
              border: 2px solid #e8e8e8 !important;
              border-radius: 16px !important;
              font-size: 16px !important;
              transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
              background: rgba(255, 255, 255, 0.9) !important;
              backdrop-filter: blur(5px);
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;

              &:focus {
                border-color: #409EFF !important;
                box-shadow: 
                  0 0 0 4px rgba(64, 158, 255, 0.15),
                  0 4px 12px rgba(64, 158, 255, 0.1) !important;
                background: rgba(255, 255, 255, 1) !important;
                transform: translateY(-2px) !important;
              }

              &:hover {
                border-color: #c0c4cc !important;
                transform: translateY(-1px) !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
              }
            }
          }

          &:hover .input-icon {
            color: #666;
            transform: translateY(-50%) scale(1.1);
          }
        }

        .custom-select {
          width: 100%;

          .el-input__inner {
            height: 55px;
            border: 2px solid #e8e8e8;
            border-radius: 16px;
            font-size: 16px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

            &:focus {
              border-color: #409EFF;
              box-shadow: 
                0 0 0 4px rgba(64, 158, 255, 0.15),
                0 4px 12px rgba(64, 158, 255, 0.1);
              background: rgba(255, 255, 255, 1);
              transform: translateY(-2px);
            }

            &:hover {
              border-color: #c0c4cc;
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            }
          }
        }
      }

      .captcha-section {
        margin-bottom: 30px;

        .captcha-group {
          display: flex;
          align-items: center;
          gap: 15px;

          .captcha-input {
            flex: 1;
            margin-bottom: 0;
          }

          .captcha-image {
            width: 120px;
            height: 55px;
            border-radius: 16px;
            overflow: hidden;
            cursor: pointer;
            position: relative;
            border: 2px solid #e8e8e8;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

            &:hover {
              border-color: #409EFF;
              transform: scale(1.02);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            }

            .captcha-img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .refresh-tip {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              background: rgba(0, 0, 0, 0.7);
              color: #fff;
              font-size: 12px;
              text-align: center;
              padding: 2px;
              opacity: 0;
              transition: opacity 0.3s ease;
            }

            &:hover .refresh-tip {
              opacity: 1;
            }
          }
        }
      }

      .register-btn-item {
        margin-bottom: 25px;

        .register-btn {
          width: 100%;
          height: 55px;
          border-radius: 16px;
          font-size: 17px;
          font-weight: 700;
          transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
          position: relative;
          overflow: hidden;
          border: none;
          background: linear-gradient(135deg, #409EFF, #66b1ff);
          box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
          }

          &:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 12px 30px rgba(64, 158, 255, 0.4);
            background: linear-gradient(135deg, #66b1ff, #409EFF);

            &::before {
              left: 100%;
            }
          }

          &:active {
            transform: translateY(-1px) scale(0.98);
          }
        }
      }

      .login-link {
        text-align: center;
        color: #666;
        font-size: 14px;

        .link {
          color: #409EFF;
          text-decoration: none;
          font-weight: 500;
          margin-left: 5px;
          transition: color 0.3s ease;

          &:hover {
            color: #66b1ff;
          }
        }
      }
    }

    // 主题样式
    &.student-theme {
      .card-header .identity-icon {
        background: linear-gradient(135deg, #409EFF, #66b1ff);
        color: #fff;
      }
    }

    &.parent-theme {
      .card-header .identity-icon {
        background: linear-gradient(135deg, #67C23A, #85ce61);
        color: #fff;
      }
    }

    &.teacher-theme {
      .card-header .identity-icon {
        background: linear-gradient(135deg, #E6A23C, #f0c78a);
        color: #fff;
      }
    }
  }

  .decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 0;

    .circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;

      &.circle-1 {
        width: 100px;
        height: 100px;
        top: 20%;
        left: 10%;
        animation-delay: 0s;
      }

      &.circle-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: 10%;
        animation-delay: 2s;
      }

      &.circle-3 {
        width: 80px;
        height: 80px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
      }
    }
  }
}

// 动画定义
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes backgroundShift {
  0%, 100% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(-15px) translateY(-8px);
  }
  50% {
    transform: translateX(15px) translateY(8px);
  }
  75% {
    transform: translateX(-8px) translateY(15px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .register {
    padding: 10px;

    .register-container {
      max-width: 100%;
    }

    .register-card {
      padding: 30px 20px;
      margin-top: 40px;

      .card-header .identity-icon {
        width: 60px;
        height: 60px;

        .icon {
          font-size: 2rem;
        }
      }

      .identity-switch {
        .switch-item {
          padding: 10px 4px;
          font-size: 12px;

          .switch-icon {
            font-size: 14px;
          }
        }
      }

      .register-form {
        .form-section {
          .form-row {
            flex-direction: column;
            gap: 0;

            .form-item-half {
              margin-bottom: 20px;
            }
          }
        }
      }
    }

    .back-btn {
      top: -50px;
    }
  }
}
</style>
