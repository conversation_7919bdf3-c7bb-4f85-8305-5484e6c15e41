-- 添加学生、家长、教师角色
INSERT INTO sys_role VALUES('3', '学生', 'student', 3, 2, 1, 1, '0', '0', 'admin', sysdate(), '', null, '学生角色');
INSERT INTO sys_role VALUES('4', '家长', 'parent', 4, 2, 1, 1, '0', '0', 'admin', sysdate(), '', null, '家长角色');
INSERT INTO sys_role VALUES('5', '教师', 'teacher', 5, 2, 1, 1, '0', '0', 'admin', sysdate(), '', null, '教师角色');

-- 为学生角色分配菜单权限（示例：只分配学生相关的菜单）
-- 这里需要根据实际的菜单ID来分配，以下是示例
INSERT INTO sys_role_menu VALUES(3, 2000); -- 学生首页
INSERT INTO sys_role_menu VALUES(3, 2001); -- 学习课程
INSERT INTO sys_role_menu VALUES(3, 2002); -- 作业提交
INSERT INTO sys_role_menu VALUES(3, 2003); -- 学习进度

-- 为家长角色分配菜单权限
INSERT INTO sys_role_menu VALUES(4, 3000); -- 家长首页
INSERT INTO sys_role_menu VALUES(4, 3001); -- 学生管理
INSERT INTO sys_role_menu VALUES(4, 3002); -- 学习监控
INSERT INTO sys_role_menu VALUES(4, 3003); -- 消息通知

-- 为教师角色分配菜单权限
INSERT INTO sys_role_menu VALUES(5, 4000); -- 教师首页
INSERT INTO sys_role_menu VALUES(5, 4001); -- 学生管理
INSERT INTO sys_role_menu VALUES(5, 4002); -- 课程管理
INSERT INTO sys_role_menu VALUES(5, 4003); -- 作业批改
INSERT INTO sys_role_menu VALUES(5, 4004); -- 成绩管理
