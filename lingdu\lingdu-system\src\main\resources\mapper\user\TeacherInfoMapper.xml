<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lingdu.system.mapper.TeacherInfoMapper">
    
    <resultMap type="TeacherInfo" id="TeacherInfoResult">
        <result property="teacherId"    column="teacher_id"    />
        <result property="userId"    column="user_id"    />
        <result property="phone"    column="phone"    />
        <result property="name"    column="name"    />
        <result property="certificateNo"    column="certificate_no"    />
        <result property="verifyStatus"    column="verify_status"    />
        <result property="stages"    column="stages"    />
        <result property="subjects"    column="subjects"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTeacherInfoVo">
        select teacher_id, user_id, phone, name, certificate_no, verify_status, stages, subjects, create_time, update_time from teacher_info
    </sql>

    <select id="selectTeacherInfoList" parameterType="TeacherInfo" resultMap="TeacherInfoResult">
        <include refid="selectTeacherInfoVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="certificateNo != null  and certificateNo != ''"> and certificate_no = #{certificateNo}</if>
            <if test="verifyStatus != null "> and verify_status = #{verifyStatus}</if>
            <if test="stages != null  and stages != ''"> and stages = #{stages}</if>
            <if test="subjects != null  and subjects != ''"> and subjects = #{subjects}</if>
        </where>
    </select>
    
    <select id="selectTeacherInfoByTeacherId" parameterType="Long" resultMap="TeacherInfoResult">
        <include refid="selectTeacherInfoVo"/>
        where teacher_id = #{teacherId}
    </select>

    <insert id="insertTeacherInfo" parameterType="TeacherInfo" useGeneratedKeys="true" keyProperty="teacherId">
        insert into teacher_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="certificateNo != null and certificateNo != ''">certificate_no,</if>
            <if test="verifyStatus != null">verify_status,</if>
            <if test="stages != null and stages != ''">stages,</if>
            <if test="subjects != null and subjects != ''">subjects,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="certificateNo != null and certificateNo != ''">#{certificateNo},</if>
            <if test="verifyStatus != null">#{verifyStatus},</if>
            <if test="stages != null and stages != ''">#{stages},</if>
            <if test="subjects != null and subjects != ''">#{subjects},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTeacherInfo" parameterType="TeacherInfo">
        update teacher_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="certificateNo != null and certificateNo != ''">certificate_no = #{certificateNo},</if>
            <if test="verifyStatus != null">verify_status = #{verifyStatus},</if>
            <if test="stages != null and stages != ''">stages = #{stages},</if>
            <if test="subjects != null and subjects != ''">subjects = #{subjects},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where teacher_id = #{teacherId}
    </update>

    <delete id="deleteTeacherInfoByTeacherId" parameterType="Long">
        delete from teacher_info where teacher_id = #{teacherId}
    </delete>

    <delete id="deleteTeacherInfoByTeacherIds" parameterType="String">
        delete from teacher_info where teacher_id in 
        <foreach item="teacherId" collection="array" open="(" separator="," close=")">
            #{teacherId}
        </foreach>
    </delete>
</mapper>