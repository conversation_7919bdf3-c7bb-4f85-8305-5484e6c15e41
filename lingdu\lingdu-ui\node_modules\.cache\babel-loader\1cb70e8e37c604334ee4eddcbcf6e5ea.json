{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\api\\login.js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\api\\login.js", "mtime": 1758269492182}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\babel.config.js", "mtime": 1758263450968}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758264043508}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9zdHVkeS9ydW95aS9saW5nZHUvbGluZ2R1L2xpbmdkdS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0Q29kZUltZyA9IGdldENvZGVJbWc7CmV4cG9ydHMuZ2V0SW5mbyA9IGdldEluZm87CmV4cG9ydHMubG9naW4gPSBsb2dpbjsKZXhwb3J0cy5sb2dvdXQgPSBsb2dvdXQ7CmV4cG9ydHMucmVnaXN0ZXIgPSByZWdpc3RlcjsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOeZu+W9leaWueazlQpmdW5jdGlvbiBsb2dpbih1c2VybmFtZSwgcGFzc3dvcmQsIGNvZGUsIHV1aWQpIHsKICB2YXIgZGF0YSA9IHsKICAgIHVzZXJuYW1lOiB1c2VybmFtZSwKICAgIHBhc3N3b3JkOiBwYXNzd29yZCwKICAgIGNvZGU6IGNvZGUsCiAgICB1dWlkOiB1dWlkCiAgfTsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9sb2dpbicsCiAgICBoZWFkZXJzOiB7CiAgICAgIGlzVG9rZW46IGZhbHNlLAogICAgICByZXBlYXRTdWJtaXQ6IGZhbHNlCiAgICB9LAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOazqOWGjOaWueazlQpmdW5jdGlvbiByZWdpc3RlcihkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcmVnaXN0ZXInLAogICAgaGVhZGVyczogewogICAgICBpc1Rva2VuOiBmYWxzZQogICAgfSwKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDojrflj5bnlKjmiLfor6bnu4bkv6Hmga8KZnVuY3Rpb24gZ2V0SW5mbygpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9nZXRJbmZvJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g6YCA5Ye65pa55rOVCmZ1bmN0aW9uIGxvZ291dCgpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9sb2dvdXQnLAogICAgbWV0aG9kOiAncG9zdCcKICB9KTsKfQoKLy8g6I635Y+W6aqM6K+B56CBCmZ1bmN0aW9uIGdldENvZGVJbWcoKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvY2FwdGNoYUltYWdlJywKICAgIGhlYWRlcnM6IHsKICAgICAgaXNUb2tlbjogZmFsc2UKICAgIH0sCiAgICBtZXRob2Q6ICdnZXQnLAogICAgdGltZW91dDogMjAwMDAKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "login", "username", "password", "code", "uuid", "data", "request", "url", "headers", "isToken", "repeatSubmit", "method", "register", "getInfo", "logout", "getCodeImg", "timeout"], "sources": ["E:/study/ruoyi/lingdu/lingdu/lingdu-ui/src/api/login.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 登录方法\r\nexport function login(username, password, code, uuid) {\r\n  const data = {\r\n    username,\r\n    password,\r\n    code,\r\n    uuid\r\n  }\r\n  return request({\r\n    url: '/login',\r\n    headers: {\r\n      isToken: false,\r\n      repeatSubmit: false\r\n    },\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 注册方法\r\nexport function register(data) {\r\n  return request({\r\n    url: '/register',\r\n    headers: {\r\n      isToken: false\r\n    },\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 获取用户详细信息\r\nexport function getInfo() {\r\n  return request({\r\n    url: '/getInfo',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 退出方法\r\nexport function logout() {\r\n  return request({\r\n    url: '/logout',\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 获取验证码\r\nexport function getCodeImg() {\r\n  return request({\r\n    url: '/captchaImage',\r\n    headers: {\r\n      isToken: false\r\n    },\r\n    method: 'get',\r\n    timeout: 20000\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,KAAKA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACpD,IAAMC,IAAI,GAAG;IACXJ,QAAQ,EAARA,QAAQ;IACRC,QAAQ,EAARA,QAAQ;IACRC,IAAI,EAAJA,IAAI;IACJC,IAAI,EAAJA;EACF,CAAC;EACD,OAAO,IAAAE,gBAAO,EAAC;IACbC,GAAG,EAAE,QAAQ;IACbC,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,YAAY,EAAE;IAChB,CAAC;IACDC,MAAM,EAAE,MAAM;IACdN,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,QAAQA,CAACP,IAAI,EAAE;EAC7B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDE,MAAM,EAAE,MAAM;IACdN,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,OAAOA,CAAA,EAAG;EACxB,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,UAAU;IACfI,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,MAAMA,CAAA,EAAG;EACvB,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,SAAS;IACdI,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,UAAUA,CAAA,EAAG;EAC3B,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDE,MAAM,EAAE,KAAK;IACbK,OAAO,EAAE;EACX,CAAC,CAAC;AACJ", "ignoreList": []}]}