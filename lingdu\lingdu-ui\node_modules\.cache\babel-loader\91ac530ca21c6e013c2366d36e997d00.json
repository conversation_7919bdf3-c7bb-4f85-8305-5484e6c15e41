{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\register.vue", "mtime": 1758270624441}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\babel.config.js", "mtime": 1758263450968}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758264043508}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758264043933}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "name", "data", "_this", "equalToPassword", "rule", "value", "callback", "registerForm", "password", "Error", "title", "process", "env", "VUE_APP_TITLE", "codeUrl", "userType", "$route", "query", "type", "username", "confirmPassword", "phone", "realName", "grade", "stage", "mainSubject", "extraSubjects", "certificateNo", "stages", "subjects", "code", "uuid", "identityTypes", "label", "icon", "registerRules", "required", "trigger", "message", "min", "max", "pattern", "validator", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created", "getCode", "methods", "_this2", "getCodeImg", "then", "res", "undefined", "img", "switchIdentity", "_this3", "$nextTick", "$refs", "clearValidate", "getIdentityIcon", "_this4", "find", "t", "getIdentityTitle", "_this5", "concat", "getIdentitySubtitle", "subtitles", "student", "parent", "teacher", "getRegisterButtonText", "texts", "handleRegister", "_this6", "validate", "valid", "register", "$alert", "dangerouslyUseHTMLString", "$router", "push", "path", "catch"], "sources": ["src/views/register.vue"], "sourcesContent": ["<template>\n  <div class=\"register\">\n    <div class=\"register-container\">\n\n\n      <!-- 注册卡片 -->\n      <div class=\"register-card\" :class=\"`${registerForm.userType}-theme`\">\n        <div class=\"card-header\">\n          <div class=\"identity-icon\">\n            <svg-icon :icon-class=\"getIdentityIcon()\" class=\"icon\" />\n          </div>\n          <h2 class=\"card-title\">{{ getIdentityTitle() }}</h2>\n          <p class=\"card-subtitle\">{{ getIdentitySubtitle() }}</p>\n        </div>\n\n        <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\n          <!-- 身份切换 -->\n          <div class=\"identity-switch\">\n            <div \n              v-for=\"type in identityTypes\" \n              :key=\"type.value\"\n              class=\"switch-item\"\n              :class=\"{ active: registerForm.userType === type.value }\"\n              @click=\"switchIdentity(type.value)\"\n            >\n              <svg-icon :icon-class=\"type.icon\" class=\"switch-icon\" />\n              <span>{{ type.label }}</span>\n            </div>\n          </div>\n\n          <!-- 基本信息 -->\n          <div class=\"form-section\">\n            <h4 class=\"section-title\">基本信息</h4>\n            \n            <div class=\"form-row\">\n              <el-form-item prop=\"username\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"user\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.username\"\n                    type=\"text\"\n                    auto-complete=\"off\"\n                    placeholder=\"请输入账号\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"phone\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"phone\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.phone\"\n                    type=\"text\"\n                    auto-complete=\"off\"\n                    placeholder=\"请输入手机号\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n\n            <el-form-item prop=\"realName\">\n              <div class=\"input-group\">\n                <svg-icon icon-class=\"user\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"registerForm.realName\"\n                  type=\"text\"\n                  auto-complete=\"off\"\n                  placeholder=\"请输入真实姓名\"\n                  class=\"custom-input\"\n                />\n              </div>\n            </el-form-item>\n\n            <div class=\"form-row\">\n              <el-form-item prop=\"password\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"password\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.password\"\n                    type=\"password\"\n                    auto-complete=\"off\"\n                    placeholder=\"请输入密码\"\n                    class=\"custom-input\"\n                    @keyup.enter.native=\"handleRegister\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"confirmPassword\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"password\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.confirmPassword\"\n                    type=\"password\"\n                    auto-complete=\"off\"\n                    placeholder=\"确认密码\"\n                    class=\"custom-input\"\n                    @keyup.enter.native=\"handleRegister\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 学生专用信息 -->\n          <div v-if=\"registerForm.userType === 'student'\" class=\"form-section\">\n            <h4 class=\"section-title\">学习信息</h4>\n            \n            <div class=\"form-row\">\n              <el-form-item prop=\"grade\" class=\"form-item-half\">\n                <el-select v-model=\"registerForm.grade\" placeholder=\"请选择年级\" class=\"custom-select\">\n                  <el-option label=\"小学一年级\" value=\"小学一年级\"></el-option>\n                  <el-option label=\"小学二年级\" value=\"小学二年级\"></el-option>\n                  <el-option label=\"小学三年级\" value=\"小学三年级\"></el-option>\n                  <el-option label=\"小学四年级\" value=\"小学四年级\"></el-option>\n                  <el-option label=\"小学五年级\" value=\"小学五年级\"></el-option>\n                  <el-option label=\"小学六年级\" value=\"小学六年级\"></el-option>\n                  <el-option label=\"初中一年级\" value=\"初中一年级\"></el-option>\n                  <el-option label=\"初中二年级\" value=\"初中二年级\"></el-option>\n                  <el-option label=\"初中三年级\" value=\"初中三年级\"></el-option>\n                  <el-option label=\"高中一年级\" value=\"高中一年级\"></el-option>\n                  <el-option label=\"高中二年级\" value=\"高中二年级\"></el-option>\n                  <el-option label=\"高中三年级\" value=\"高中三年级\"></el-option>\n                </el-select>\n              </el-form-item>\n\n              <el-form-item prop=\"stage\" class=\"form-item-half\">\n                <el-select v-model=\"registerForm.stage\" placeholder=\"请选择学段\" class=\"custom-select\">\n                  <el-option label=\"小学\" value=\"小学\"></el-option>\n                  <el-option label=\"初中\" value=\"初中\"></el-option>\n                  <el-option label=\"高中\" value=\"高中\"></el-option>\n                </el-select>\n              </el-form-item>\n            </div>\n\n            <div class=\"form-row\">\n              <el-form-item prop=\"mainSubject\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.mainSubject\"\n                    type=\"text\"\n                    placeholder=\"主学科（如：数学）\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"extraSubjects\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.extraSubjects\"\n                    type=\"text\"\n                    placeholder=\"副学科\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 教师专用信息 -->\n          <div v-if=\"registerForm.userType === 'teacher'\" class=\"form-section\">\n            <h4 class=\"section-title\">教学信息</h4>\n            \n            <el-form-item prop=\"certificateNo\">\n              <div class=\"input-group\">\n                <svg-icon icon-class=\"documentation\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"registerForm.certificateNo\"\n                  type=\"text\"\n                  placeholder=\"请输入教师资格证编号\"\n                  class=\"custom-input\"\n                />\n              </div>\n            </el-form-item>\n\n            <div class=\"form-row\">\n              <el-form-item prop=\"stages\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.stages\"\n                    type=\"text\"\n                    placeholder=\"教授学段（如：小学、初中、高中）\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"subjects\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.subjects\"\n                    type=\"text\"\n                    placeholder=\"教授学科（如：数学、语文、英语）\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 验证码 -->\n          <el-form-item prop=\"code\" v-if=\"captchaEnabled\" class=\"captcha-section\">\n            <div class=\"captcha-group\">\n              <div class=\"input-group captcha-input\">\n                <svg-icon icon-class=\"validCode\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"registerForm.code\"\n                  auto-complete=\"off\"\n                  placeholder=\"请输入验证码\"\n                  class=\"custom-input\"\n                  @keyup.enter.native=\"handleRegister\"\n                />\n              </div>\n              <div class=\"captcha-image\" @click=\"getCode\">\n                <img :src=\"codeUrl\" class=\"captcha-img\"/>\n                <div class=\"refresh-tip\">点击刷新</div>\n              </div>\n            </div>\n          </el-form-item>\n\n          <!-- 注册按钮 -->\n          <el-form-item class=\"register-btn-item\">\n            <el-button\n              :loading=\"loading\"\n              type=\"primary\"\n              class=\"register-btn\"\n              @click.native.prevent=\"handleRegister\"\n            >\n              <span v-if=\"!loading\">{{ getRegisterButtonText() }}</span>\n              <span v-else>注册中...</span>\n            </el-button>\n          </el-form-item>\n\n          <div class=\"login-link\">\n            <span>已有账号？</span>\n            <router-link to=\"/login\" class=\"link\">立即登录</router-link>\n          </div>\n        </el-form>\n      </div>\n\n      <!-- 装饰性元素 -->\n      <div class=\"decoration\">\n        <div class=\"circle circle-1\"></div>\n        <div class=\"circle circle-2\"></div>\n        <div class=\"circle circle-3\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg, register } from \"@/api/login\"\n\nexport default {\n  name: \"Register\",\n  data() {\n    const equalToPassword = (rule, value, callback) => {\n      if (this.registerForm.password !== value) {\n        callback(new Error(\"两次输入的密码不一致\"))\n      } else {\n        callback()\n      }\n    }\n    return {\n      title: process.env.VUE_APP_TITLE,\n      codeUrl: \"\",\n      registerForm: {\n        userType: this.$route.query.type || \"student\",\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        phone: \"\",\n        realName: \"\",\n        grade: \"\",\n        stage: \"\",\n        mainSubject: \"\",\n        extraSubjects: \"\",\n        certificateNo: \"\",\n        stages: \"\",\n        subjects: \"\",\n        code: \"\",\n        uuid: \"\"\n      },\n      identityTypes: [\n        { value: \"student\", label: \"学生\", icon: \"education\" },\n        { value: \"parent\", label: \"家长\", icon: \"peoples\" },\n        { value: \"teacher\", label: \"教师\", icon: \"user\" }\n      ],\n      registerRules: {\n        userType: [\n          { required: true, trigger: \"change\", message: \"请选择用户类型\" }\n        ],\n        username: [\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\n          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\n          { min: 5, max: 20, message: \"用户密码长度必须介于 5 和 20 之间\", trigger: \"blur\" },\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\ |\", trigger: \"blur\" }\n        ],\n        confirmPassword: [\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\n        ],\n        phone: [\n          { required: true, trigger: \"blur\", message: \"请输入手机号\" },\n          { pattern: /^1[3-9]\\d{9}$/, message: \"请输入正确的手机号格式\", trigger: \"blur\" }\n        ],\n        realName: [\n          { required: true, trigger: \"blur\", message: \"请输入真实姓名\" }\n        ],\n        grade: [\n          { required: true, trigger: \"change\", message: \"请选择年级\" }\n        ],\n        stage: [\n          { required: true, trigger: \"change\", message: \"请选择学段\" }\n        ],\n        mainSubject: [\n          { required: true, trigger: \"blur\", message: \"请输入主学科\" }\n        ],\n        certificateNo: [\n          { required: true, trigger: \"blur\", message: \"请输入教师资格证编号\" }\n        ],\n        stages: [\n          { required: true, trigger: \"blur\", message: \"请输入教授学段\" }\n        ],\n        subjects: [\n          { required: true, trigger: \"blur\", message: \"请输入教授学科\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      captchaEnabled: true\n    }\n  },\n  created() {\n    this.getCode()\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img\n          this.registerForm.uuid = res.uuid\n        }\n      })\n    },\n    switchIdentity(type) {\n      this.registerForm.userType = type\n      // 清空特定字段\n      this.registerForm.grade = \"\"\n      this.registerForm.stage = \"\"\n      this.registerForm.mainSubject = \"\"\n      this.registerForm.extraSubjects = \"\"\n      this.registerForm.certificateNo = \"\"\n      this.registerForm.stages = \"\"\n      this.registerForm.subjects = \"\"\n      \n      // 清除验证\n      this.$nextTick(() => {\n        this.$refs.registerForm.clearValidate()\n      })\n    },\n\n    getIdentityIcon() {\n      const type = this.identityTypes.find(t => t.value === this.registerForm.userType)\n      return type ? type.icon : 'user'\n    },\n    getIdentityTitle() {\n      const type = this.identityTypes.find(t => t.value === this.registerForm.userType)\n      return type ? `${type.label}注册` : '用户注册'\n    },\n    getIdentitySubtitle() {\n      const subtitles = {\n        student: '开启您的学习之旅',\n        parent: '关注孩子的学习成长',\n        teacher: '开启您的教学管理'\n      }\n      return subtitles[this.registerForm.userType] || '欢迎加入'\n    },\n    getRegisterButtonText() {\n      const texts = {\n        student: '开始学习',\n        parent: '开始管理',\n        teacher: '开始教学'\n      }\n      return texts[this.registerForm.userType] || '注册'\n    },\n    handleRegister() {\n      this.$refs.registerForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n          register(this.registerForm).then(res => {\n            const username = this.registerForm.username\n            const userType = this.registerForm.userType\n            this.$alert(\"<font color='red'>恭喜你，您的账号 \" + username + \" 注册成功！</font>\", '系统提示', {\n              dangerouslyUseHTMLString: true,\n              type: 'success'\n            }).then(() => {\n              // 跳转到登录页面并传递用户类型参数\n              this.$router.push({\n                path: \"/login\",\n                query: { type: userType }\n              })\n            }).catch(() => {})\n          }).catch(() => {\n            this.loading = false\n            if (this.captchaEnabled) {\n              this.getCode()\n            }\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\n.register {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: url(\"../assets/images/login-background.jpg\") center/cover;\n    opacity: 0.1;\n    z-index: 0;\n  }\n\n  // 添加动态背景粒子效果\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-image: \n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);\n    animation: backgroundShift 8s ease-in-out infinite;\n    z-index: 0;\n  }\n\n  .register-container {\n    position: relative;\n    z-index: 1;\n    width: 100%;\n    max-width: 600px;\n  }\n\n\n\n  .register-card {\n    background: rgba(255, 255, 255, 0.95);\n    border-radius: 24px;\n    padding: 45px;\n    box-shadow: \n      0 25px 50px rgba(0, 0, 0, 0.15),\n      0 10px 20px rgba(0, 0, 0, 0.1),\n      inset 0 1px 0 rgba(255, 255, 255, 0.6);\n    backdrop-filter: blur(15px);\n    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n    animation: slideInUp 0.8s ease-out;\n    position: relative;\n    overflow: hidden;\n    border: 1px solid rgba(255, 255, 255, 0.2);\n\n    // 光泽效果\n    &::before {\n      content: '';\n      position: absolute;\n      top: -50%;\n      left: -50%;\n      width: 200%;\n      height: 200%;\n      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);\n      transform: rotate(45deg);\n      transition: all 0.6s ease;\n      opacity: 0;\n      pointer-events: none;\n      z-index: 1;\n    }\n\n    // 边框光效\n    &::after {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      border-radius: 24px;\n      padding: 2px;\n      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n      mask-composite: exclude;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n      pointer-events: none;\n      z-index: 1;\n    }\n\n    &:hover {\n      transform: translateY(-8px) scale(1.02);\n      box-shadow: \n        0 35px 70px rgba(0, 0, 0, 0.25),\n        0 15px 30px rgba(0, 0, 0, 0.15),\n        inset 0 1px 0 rgba(255, 255, 255, 0.8);\n\n      &::before {\n        opacity: 1;\n        transform: rotate(45deg) translate(50%, 50%);\n      }\n\n      &::after {\n        opacity: 1;\n      }\n    }\n\n    .card-header {\n      text-align: center;\n      margin-bottom: 30px;\n\n      .identity-icon {\n        width: 80px;\n        height: 80px;\n        margin: 0 auto 20px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.3s ease;\n\n        .icon {\n          font-size: 2.5rem;\n        }\n      }\n\n      .card-title {\n        font-size: 1.8rem;\n        font-weight: 600;\n        margin: 0 0 10px 0;\n        color: #333;\n      }\n\n      .card-subtitle {\n        font-size: 1rem;\n        color: #666;\n        margin: 0;\n      }\n    }\n\n    .identity-switch {\n      display: flex;\n      background: rgba(245, 245, 245, 0.8);\n      border-radius: 16px;\n      padding: 6px;\n      margin-bottom: 35px;\n      backdrop-filter: blur(5px);\n      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);\n\n      .switch-item {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 14px 10px;\n        border-radius: 12px;\n        cursor: pointer;\n        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n        font-size: 15px;\n        font-weight: 600;\n        position: relative;\n        overflow: hidden;\n\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));\n          border-radius: 12px;\n          opacity: 0;\n          transition: opacity 0.3s ease;\n          pointer-events: none;\n        }\n\n        .switch-icon {\n          margin-right: 8px;\n          font-size: 18px;\n          transition: all 0.3s ease;\n        }\n\n        &.active {\n          background: #fff;\n          color: #409EFF;\n          box-shadow: \n            0 4px 12px rgba(0, 0, 0, 0.15),\n            0 2px 4px rgba(64, 158, 255, 0.2);\n          transform: translateY(-2px);\n\n          &::before {\n            opacity: 1;\n          }\n\n          .switch-icon {\n            transform: scale(1.1);\n          }\n        }\n\n        &:hover:not(.active) {\n          background: rgba(255, 255, 255, 0.9);\n          transform: translateY(-1px);\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n        }\n      }\n    }\n\n    .register-form {\n      position: relative;\n      z-index: 2;\n      \n      .form-section {\n        margin-bottom: 30px;\n\n        .section-title {\n          font-size: 1.1rem;\n          font-weight: 600;\n          color: #333;\n          margin: 0 0 20px 0;\n          padding-bottom: 10px;\n          border-bottom: 2px solid #f0f0f0;\n        }\n\n        .form-row {\n          display: flex;\n          gap: 15px;\n          margin-bottom: 20px;\n\n          .form-item-half {\n            flex: 1;\n            margin-bottom: 0;\n          }\n        }\n\n        .input-group {\n          position: relative;\n          margin-bottom: 25px;\n\n          .input-icon {\n            position: absolute;\n            left: 20px;\n            top: 50%;\n            transform: translateY(-50%);\n            color: #999;\n            font-size: 16px;\n            z-index: 3;\n            transition: all 0.3s ease;\n          }\n\n          .custom-input {\n            ::v-deep .el-input__inner {\n              height: 55px !important;\n              padding-left: 60px !important;\n              border: 2px solid #e8e8e8 !important;\n              border-radius: 16px !important;\n              font-size: 16px !important;\n              transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;\n              background: rgba(255, 255, 255, 0.9) !important;\n              backdrop-filter: blur(5px);\n              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;\n\n              &:focus {\n                border-color: #409EFF !important;\n                box-shadow: \n                  0 0 0 4px rgba(64, 158, 255, 0.15),\n                  0 4px 12px rgba(64, 158, 255, 0.1) !important;\n                background: rgba(255, 255, 255, 1) !important;\n                transform: translateY(-2px) !important;\n              }\n\n              &:hover {\n                border-color: #c0c4cc !important;\n                transform: translateY(-1px) !important;\n                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;\n              }\n            }\n          }\n\n          &:hover .input-icon {\n            color: #666;\n            transform: translateY(-50%) scale(1.1);\n          }\n        }\n\n        .custom-select {\n          width: 100%;\n\n          .el-input__inner {\n            height: 55px;\n            border: 2px solid #e8e8e8;\n            border-radius: 16px;\n            font-size: 16px;\n            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n            background: rgba(255, 255, 255, 0.9);\n            backdrop-filter: blur(5px);\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n\n            &:focus {\n              border-color: #409EFF;\n              box-shadow: \n                0 0 0 4px rgba(64, 158, 255, 0.15),\n                0 4px 12px rgba(64, 158, 255, 0.1);\n              background: rgba(255, 255, 255, 1);\n              transform: translateY(-2px);\n            }\n\n            &:hover {\n              border-color: #c0c4cc;\n              transform: translateY(-1px);\n              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n            }\n          }\n        }\n      }\n\n      .captcha-section {\n        margin-bottom: 30px;\n\n        .captcha-group {\n          display: flex;\n          align-items: center;\n          gap: 15px;\n\n          .captcha-input {\n            flex: 1;\n            margin-bottom: 0;\n          }\n\n          .captcha-image {\n            width: 120px;\n            height: 55px;\n            border-radius: 16px;\n            overflow: hidden;\n            cursor: pointer;\n            position: relative;\n            border: 2px solid #e8e8e8;\n            transition: all 0.3s ease;\n            background: rgba(255, 255, 255, 0.9);\n            backdrop-filter: blur(5px);\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n\n            &:hover {\n              border-color: #409EFF;\n              transform: scale(1.02);\n              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n            }\n\n            .captcha-img {\n              width: 100%;\n              height: 100%;\n              object-fit: cover;\n            }\n\n            .refresh-tip {\n              position: absolute;\n              bottom: 0;\n              left: 0;\n              right: 0;\n              background: rgba(0, 0, 0, 0.7);\n              color: #fff;\n              font-size: 12px;\n              text-align: center;\n              padding: 2px;\n              opacity: 0;\n              transition: opacity 0.3s ease;\n            }\n\n            &:hover .refresh-tip {\n              opacity: 1;\n            }\n          }\n        }\n      }\n\n      .register-btn-item {\n        margin-bottom: 25px;\n\n        .register-btn {\n          width: 100%;\n          height: 55px;\n          border-radius: 16px;\n          font-size: 17px;\n          font-weight: 700;\n          transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n          position: relative;\n          overflow: hidden;\n          border: none;\n          background: linear-gradient(135deg, #409EFF, #66b1ff);\n          box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);\n\n          &::before {\n            content: '';\n            position: absolute;\n            top: 0;\n            left: -100%;\n            width: 100%;\n            height: 100%;\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n            transition: left 0.5s ease;\n          }\n\n          &:hover {\n            transform: translateY(-3px) scale(1.02);\n            box-shadow: 0 12px 30px rgba(64, 158, 255, 0.4);\n            background: linear-gradient(135deg, #66b1ff, #409EFF);\n\n            &::before {\n              left: 100%;\n            }\n          }\n\n          &:active {\n            transform: translateY(-1px) scale(0.98);\n          }\n        }\n      }\n\n      .login-link {\n        text-align: center;\n        color: #666;\n        font-size: 14px;\n\n        .link {\n          color: #409EFF;\n          text-decoration: none;\n          font-weight: 500;\n          margin-left: 5px;\n          transition: color 0.3s ease;\n\n          &:hover {\n            color: #66b1ff;\n          }\n        }\n      }\n    }\n\n    // 主题样式\n    &.student-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #409EFF, #66b1ff);\n        color: #fff;\n      }\n    }\n\n    &.parent-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #67C23A, #85ce61);\n        color: #fff;\n      }\n    }\n\n    &.teacher-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #E6A23C, #f0c78a);\n        color: #fff;\n      }\n    }\n  }\n\n  .decoration {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    pointer-events: none;\n    z-index: 0;\n\n    .circle {\n      position: absolute;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      animation: float 6s ease-in-out infinite;\n\n      &.circle-1 {\n        width: 100px;\n        height: 100px;\n        top: 20%;\n        left: 10%;\n        animation-delay: 0s;\n      }\n\n      &.circle-2 {\n        width: 150px;\n        height: 150px;\n        top: 60%;\n        right: 10%;\n        animation-delay: 2s;\n      }\n\n      &.circle-3 {\n        width: 80px;\n        height: 80px;\n        bottom: 20%;\n        left: 20%;\n        animation-delay: 4s;\n      }\n    }\n  }\n}\n\n// 动画定义\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n@keyframes backgroundShift {\n  0%, 100% {\n    transform: translateX(0) translateY(0);\n  }\n  25% {\n    transform: translateX(-15px) translateY(-8px);\n  }\n  50% {\n    transform: translateX(15px) translateY(8px);\n  }\n  75% {\n    transform: translateX(-8px) translateY(15px);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n@keyframes shimmer {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .register {\n    padding: 10px;\n\n    .register-container {\n      max-width: 100%;\n    }\n\n    .register-card {\n      padding: 30px 20px;\n      margin-top: 40px;\n\n      .card-header .identity-icon {\n        width: 60px;\n        height: 60px;\n\n        .icon {\n          font-size: 2rem;\n        }\n      }\n\n      .identity-switch {\n        .switch-item {\n          padding: 10px 4px;\n          font-size: 12px;\n\n          .switch-icon {\n            font-size: 14px;\n          }\n        }\n      }\n\n      .register-form {\n        .form-section {\n          .form-row {\n            flex-direction: column;\n            gap: 0;\n\n            .form-item-half {\n              margin-bottom: 20px;\n            }\n          }\n        }\n      }\n    }\n\n\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;AAkQA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,eAAA,YAAAA,gBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAJ,KAAA,CAAAK,YAAA,CAAAC,QAAA,KAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IACA;MACAI,KAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,aAAA;MACAC,OAAA;MACAP,YAAA;QACAQ,QAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA;QACAC,QAAA;QACAX,QAAA;QACAY,eAAA;QACAC,KAAA;QACAC,QAAA;QACAC,KAAA;QACAC,KAAA;QACAC,WAAA;QACAC,aAAA;QACAC,aAAA;QACAC,MAAA;QACAC,QAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,aAAA,GACA;QAAA3B,KAAA;QAAA4B,KAAA;QAAAC,IAAA;MAAA,GACA;QAAA7B,KAAA;QAAA4B,KAAA;QAAAC,IAAA;MAAA,GACA;QAAA7B,KAAA;QAAA4B,KAAA;QAAAC,IAAA;MAAA,EACA;MACAC,aAAA;QACApB,QAAA,GACA;UAAAqB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,QAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAF,OAAA;UAAAD,OAAA;QAAA,EACA;QACA7B,QAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAF,OAAA;UAAAD,OAAA;QAAA,GACA;UAAAI,OAAA;UAAAH,OAAA;UAAAD,OAAA;QAAA,EACA;QACAjB,eAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAM,SAAA,EAAAvC,eAAA;UAAAkC,OAAA;QAAA,EACA;QACAhB,KAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,OAAA;UAAAH,OAAA;UAAAD,OAAA;QAAA,EACA;QACAf,QAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,KAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,KAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,WAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,aAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,MAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,QAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,IAAA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAK,OAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,iBAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAAJ,cAAA,GAAAO,GAAA,CAAAP,cAAA,KAAAQ,SAAA,UAAAD,GAAA,CAAAP,cAAA;QACA,IAAAI,MAAA,CAAAJ,cAAA;UACAI,MAAA,CAAAlC,OAAA,8BAAAqC,GAAA,CAAAE,GAAA;UACAL,MAAA,CAAAzC,YAAA,CAAAwB,IAAA,GAAAoB,GAAA,CAAApB,IAAA;QACA;MACA;IACA;IACAuB,cAAA,WAAAA,eAAApC,IAAA;MAAA,IAAAqC,MAAA;MACA,KAAAhD,YAAA,CAAAQ,QAAA,GAAAG,IAAA;MACA;MACA,KAAAX,YAAA,CAAAgB,KAAA;MACA,KAAAhB,YAAA,CAAAiB,KAAA;MACA,KAAAjB,YAAA,CAAAkB,WAAA;MACA,KAAAlB,YAAA,CAAAmB,aAAA;MACA,KAAAnB,YAAA,CAAAoB,aAAA;MACA,KAAApB,YAAA,CAAAqB,MAAA;MACA,KAAArB,YAAA,CAAAsB,QAAA;;MAEA;MACA,KAAA2B,SAAA;QACAD,MAAA,CAAAE,KAAA,CAAAlD,YAAA,CAAAmD,aAAA;MACA;IACA;IAEAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,IAAA1C,IAAA,QAAAc,aAAA,CAAA6B,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAzD,KAAA,KAAAuD,MAAA,CAAArD,YAAA,CAAAQ,QAAA;MAAA;MACA,OAAAG,IAAA,GAAAA,IAAA,CAAAgB,IAAA;IACA;IACA6B,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,IAAA9C,IAAA,QAAAc,aAAA,CAAA6B,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAzD,KAAA,KAAA2D,MAAA,CAAAzD,YAAA,CAAAQ,QAAA;MAAA;MACA,OAAAG,IAAA,MAAA+C,MAAA,CAAA/C,IAAA,CAAAe,KAAA;IACA;IACAiC,mBAAA,WAAAA,oBAAA;MACA,IAAAC,SAAA;QACAC,OAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA,OAAAH,SAAA,MAAA5D,YAAA,CAAAQ,QAAA;IACA;IACAwD,qBAAA,WAAAA,sBAAA;MACA,IAAAC,KAAA;QACAJ,OAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA,OAAAE,KAAA,MAAAjE,YAAA,CAAAQ,QAAA;IACA;IACA0D,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAjB,KAAA,CAAAlD,YAAA,CAAAoE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAA/B,OAAA;UACA,IAAAkC,eAAA,EAAAH,MAAA,CAAAnE,YAAA,EAAA2C,IAAA,WAAAC,GAAA;YACA,IAAAhC,QAAA,GAAAuD,MAAA,CAAAnE,YAAA,CAAAY,QAAA;YACA,IAAAJ,QAAA,GAAA2D,MAAA,CAAAnE,YAAA,CAAAQ,QAAA;YACA2D,MAAA,CAAAI,MAAA,iCAAA3D,QAAA;cACA4D,wBAAA;cACA7D,IAAA;YACA,GAAAgC,IAAA;cACA;cACAwB,MAAA,CAAAM,OAAA,CAAAC,IAAA;gBACAC,IAAA;gBACAjE,KAAA;kBAAAC,IAAA,EAAAH;gBAAA;cACA;YACA,GAAAoE,KAAA;UACA,GAAAA,KAAA;YACAT,MAAA,CAAA/B,OAAA;YACA,IAAA+B,MAAA,CAAA9B,cAAA;cACA8B,MAAA,CAAA5B,OAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}