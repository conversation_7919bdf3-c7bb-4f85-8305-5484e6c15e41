{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\permission.js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\permission.js", "mtime": 1758270743620}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\babel.config.js", "mtime": 1758263450968}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758264043508}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_router", "_interopRequireDefault", "require", "_store", "_elementUi", "_nprogress", "_auth", "_validate", "_request", "NProgress", "configure", "showSpinner", "whiteList", "isWhiteList", "path", "some", "pattern", "isPathMatch", "router", "beforeEach", "to", "from", "next", "start", "getToken", "meta", "title", "store", "dispatch", "done", "getters", "roles", "length", "is<PERSON><PERSON>gin", "show", "then", "accessRoutes", "addRoutes", "_objectSpread2", "default", "replace", "catch", "err", "Message", "error", "concat", "encodeURIComponent", "fullPath", "after<PERSON>ach"], "sources": ["E:/study/ruoyi/lingdu/lingdu/lingdu-ui/src/permission.js"], "sourcesContent": ["import router from './router'\r\nimport store from './store'\r\nimport { Message } from 'element-ui'\r\nimport NProgress from 'nprogress'\r\nimport 'nprogress/nprogress.css'\r\nimport { getToken } from '@/utils/auth'\r\nimport { isPathMatch } from '@/utils/validate'\r\nimport { isRelogin } from '@/utils/request'\r\n\r\nNProgress.configure({ showSpinner: false })\r\n\r\nconst whiteList = ['/login', '/register']\r\n\r\nconst isWhiteList = (path) => {\r\n  return whiteList.some(pattern => isPathMatch(pattern, path))\r\n}\r\n\r\nrouter.beforeEach((to, from, next) => {\r\n  NProgress.start()\r\n  if (getToken()) {\r\n    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)\r\n    /* has token*/\r\n    if (to.path === '/login' || to.path === '/register') {\r\n      // 已登录用户访问登录或注册页面，重定向到首页\r\n      next({ path: '/index' })\r\n      NProgress.done()\r\n    } else if (isWhiteList(to.path)) {\r\n      // 白名单页面直接通过\r\n      next()\r\n    } else {\r\n      if (store.getters.roles.length === 0) {\r\n        isRelogin.show = true\r\n        // 判断当前用户是否已拉取完user_info信息\r\n        store.dispatch('GetInfo').then(() => {\r\n          isRelogin.show = false\r\n          store.dispatch('GenerateRoutes').then(accessRoutes => {\r\n            // 根据roles权限生成可访问的路由表\r\n            router.addRoutes(accessRoutes) // 动态添加可访问路由表\r\n            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成\r\n          })\r\n        }).catch(err => {\r\n            store.dispatch('LogOut').then(() => {\r\n              Message.error(err)\r\n              next({ path: '/login' })\r\n            })\r\n          })\r\n      } else {\r\n        next()\r\n      }\r\n    }\r\n  } else {\r\n    // 没有token\r\n    if (isWhiteList(to.path)) {\r\n      // 在免登录白名单，直接进入\r\n      next()\r\n    } else {\r\n      // 否则全部重定向到登录页，保留原始路径作为重定向参数\r\n      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`)\r\n      NProgress.done()\r\n    }\r\n  }\r\n})\r\n\r\nrouter.afterEach(() => {\r\n  NProgress.done()\r\n})\r\n"], "mappings": ";;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACAA,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AAEAO,kBAAS,CAACC,SAAS,CAAC;EAAEC,WAAW,EAAE;AAAM,CAAC,CAAC;AAE3C,IAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC;AAEzC,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;EAC5B,OAAOF,SAAS,CAACG,IAAI,CAAC,UAAAC,OAAO;IAAA,OAAI,IAAAC,qBAAW,EAACD,OAAO,EAAEF,IAAI,CAAC;EAAA,EAAC;AAC9D,CAAC;AAEDI,eAAM,CAACC,UAAU,CAAC,UAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAK;EACpCb,kBAAS,CAACc,KAAK,CAAC,CAAC;EACjB,IAAI,IAAAC,cAAQ,EAAC,CAAC,EAAE;IACdJ,EAAE,CAACK,IAAI,CAACC,KAAK,IAAIC,cAAK,CAACC,QAAQ,CAAC,mBAAmB,EAAER,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;IACnE;IACA,IAAIN,EAAE,CAACN,IAAI,KAAK,QAAQ,IAAIM,EAAE,CAACN,IAAI,KAAK,WAAW,EAAE;MACnD;MACAQ,IAAI,CAAC;QAAER,IAAI,EAAE;MAAS,CAAC,CAAC;MACxBL,kBAAS,CAACoB,IAAI,CAAC,CAAC;IAClB,CAAC,MAAM,IAAIhB,WAAW,CAACO,EAAE,CAACN,IAAI,CAAC,EAAE;MAC/B;MACAQ,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACL,IAAIK,cAAK,CAACG,OAAO,CAACC,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACpCC,kBAAS,CAACC,IAAI,GAAG,IAAI;QACrB;QACAP,cAAK,CAACC,QAAQ,CAAC,SAAS,CAAC,CAACO,IAAI,CAAC,YAAM;UACnCF,kBAAS,CAACC,IAAI,GAAG,KAAK;UACtBP,cAAK,CAACC,QAAQ,CAAC,gBAAgB,CAAC,CAACO,IAAI,CAAC,UAAAC,YAAY,EAAI;YACpD;YACAlB,eAAM,CAACmB,SAAS,CAACD,YAAY,CAAC,EAAC;YAC/Bd,IAAI,KAAAgB,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAMnB,EAAE;cAAEoB,OAAO,EAAE;YAAI,EAAE,CAAC,EAAC;UACjC,CAAC,CAAC;QACJ,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAC,GAAG,EAAI;UACZf,cAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAACO,IAAI,CAAC,YAAM;YAClCQ,kBAAO,CAACC,KAAK,CAACF,GAAG,CAAC;YAClBpB,IAAI,CAAC;cAAER,IAAI,EAAE;YAAS,CAAC,CAAC;UAC1B,CAAC,CAAC;QACJ,CAAC,CAAC;MACN,CAAC,MAAM;QACLQ,IAAI,CAAC,CAAC;MACR;IACF;EACF,CAAC,MAAM;IACL;IACA,IAAIT,WAAW,CAACO,EAAE,CAACN,IAAI,CAAC,EAAE;MACxB;MACAQ,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACL;MACAA,IAAI,oBAAAuB,MAAA,CAAoBC,kBAAkB,CAAC1B,EAAE,CAAC2B,QAAQ,CAAC,CAAE,CAAC;MAC1DtC,kBAAS,CAACoB,IAAI,CAAC,CAAC;IAClB;EACF;AACF,CAAC,CAAC;AAEFX,eAAM,CAAC8B,SAAS,CAAC,YAAM;EACrBvC,kBAAS,CAACoB,IAAI,CAAC,CAAC;AAClB,CAAC,CAAC", "ignoreList": []}]}