package com.lingdu.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.lingdu.common.annotation.Excel;
import com.lingdu.common.core.domain.BaseEntity;

/**
 * 家长与学生绑定关系对象 user_parent_student
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
public class UserParentStudent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 家长ID */
    @Excel(name = "家长ID")
    private Long parentId;

    /** 学生ID */
    @Excel(name = "学生ID")
    private Long studentId;

    /** 关系 */
    @Excel(name = "关系")
    private String relation;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }

    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }

    public void setRelation(String relation) 
    {
        this.relation = relation;
    }

    public String getRelation() 
    {
        return relation;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("parentId", getParentId())
            .append("studentId", getStudentId())
            .append("relation", getRelation())
            .append("createTime", getCreateTime())
            .toString();
    }
}
