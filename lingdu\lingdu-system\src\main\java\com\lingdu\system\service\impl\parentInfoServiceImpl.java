package com.lingdu.system.service.impl;

import java.util.List;
import com.lingdu.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lingdu.system.mapper.parentInfoMapper;
import com.lingdu.system.domain.parentInfo;
import com.lingdu.system.service.IparentInfoService;

/**
 * 家长信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
@Service
public class parentInfoServiceImpl implements IparentInfoService 
{
    @Autowired
    private parentInfoMapper parentInfoMapper;

    /**
     * 查询家长信息
     * 
     * @param parentId 家长信息主键
     * @return 家长信息
     */
    @Override
    public parentInfo selectparentInfoByParentId(Long parentId)
    {
        return parentInfoMapper.selectparentInfoByParentId(parentId);
    }

    /**
     * 查询家长信息列表
     * 
     * @param parentInfo 家长信息
     * @return 家长信息
     */
    @Override
    public List<parentInfo> selectparentInfoList(parentInfo parentInfo)
    {
        return parentInfoMapper.selectparentInfoList(parentInfo);
    }

    /**
     * 新增家长信息
     * 
     * @param parentInfo 家长信息
     * @return 结果
     */
    @Override
    public int insertparentInfo(parentInfo parentInfo)
    {
        parentInfo.setCreateTime(DateUtils.getNowDate());
        return parentInfoMapper.insertparentInfo(parentInfo);
    }

    /**
     * 修改家长信息
     * 
     * @param parentInfo 家长信息
     * @return 结果
     */
    @Override
    public int updateparentInfo(parentInfo parentInfo)
    {
        parentInfo.setUpdateTime(DateUtils.getNowDate());
        return parentInfoMapper.updateparentInfo(parentInfo);
    }

    /**
     * 批量删除家长信息
     * 
     * @param parentIds 需要删除的家长信息主键
     * @return 结果
     */
    @Override
    public int deleteparentInfoByParentIds(Long[] parentIds)
    {
        return parentInfoMapper.deleteparentInfoByParentIds(parentIds);
    }

    /**
     * 删除家长信息信息
     * 
     * @param parentId 家长信息主键
     * @return 结果
     */
    @Override
    public int deleteparentInfoByParentId(Long parentId)
    {
        return parentInfoMapper.deleteparentInfoByParentId(parentId);
    }
}
