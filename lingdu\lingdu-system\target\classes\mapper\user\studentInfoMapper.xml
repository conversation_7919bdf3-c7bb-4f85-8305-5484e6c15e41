<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lingdu.system.mapper.studentInfoMapper">
    
    <resultMap type="studentInfo" id="studentInfoResult">
        <result property="studentId"    column="student_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="userDisplayName"    column="user_display_name"    />
        <result property="grade"    column="grade"    />
        <result property="stage"    column="stage"    />
        <result property="mainSubject"    column="main_subject"    />
        <result property="extraSubjects"    column="extra_subjects"    />
        <result property="membershipType"    column="membership_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectstudentInfoVo">
        select si.student_id, si.nick_name, u.nick_name as user_display_name, si.grade, si.stage, si.main_subject, si.extra_subjects, si.membership_type, si.create_time, si.update_time
        from student_info si
        left join sys_user u on si.nick_name = u.nick_name
    </sql>

    <select id="selectstudentInfoList" parameterType="studentInfo" resultMap="studentInfoResult">
        <include refid="selectstudentInfoVo"/>
        <where>  
            <if test="nickName != null and nickName != ''"> and si.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="grade != null  and grade != ''"> and si.grade = #{grade}</if>
            <if test="stage != null  and stage != ''"> and si.stage = #{stage}</if>
            <if test="mainSubject != null  and mainSubject != ''"> and si.main_subject = #{mainSubject}</if>
            <if test="membershipType != null  and membershipType != ''"> and si.membership_type = #{membershipType}</if>
        </where>
    </select>
    
    <select id="selectstudentInfoByStudentId" parameterType="Long" resultMap="studentInfoResult">
        <include refid="selectstudentInfoVo"/>
        where student_id = #{studentId}
    </select>

    <insert id="insertstudentInfo" parameterType="studentInfo" useGeneratedKeys="true" keyProperty="studentId">
        insert into student_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="grade != null and grade != ''">grade,</if>
            <if test="stage != null and stage != ''">stage,</if>
            <if test="mainSubject != null and mainSubject != ''">main_subject,</if>
            <if test="extraSubjects != null">extra_subjects,</if>
            <if test="membershipType != null and membershipType != ''">membership_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="grade != null and grade != ''">#{grade},</if>
            <if test="stage != null and stage != ''">#{stage},</if>
            <if test="mainSubject != null and mainSubject != ''">#{mainSubject},</if>
            <if test="extraSubjects != null">#{extraSubjects},</if>
            <if test="membershipType != null and membershipType != ''">#{membershipType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatestudentInfo" parameterType="studentInfo">
        update student_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="grade != null and grade != ''">grade = #{grade},</if>
            <if test="stage != null and stage != ''">stage = #{stage},</if>
            <if test="mainSubject != null and mainSubject != ''">main_subject = #{mainSubject},</if>
            <if test="extraSubjects != null">extra_subjects = #{extraSubjects},</if>
            <if test="membershipType != null and membershipType != ''">membership_type = #{membershipType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where student_id = #{studentId}
    </update>

    <delete id="deletestudentInfoByStudentId" parameterType="Long">
        delete from student_info where student_id = #{studentId}
    </delete>

    <delete id="deletestudentInfoByStudentIds" parameterType="String">
        delete from student_info where student_id in 
        <foreach item="studentId" collection="array" open="(" separator="," close=")">
            #{studentId}
        </foreach>
    </delete>
</mapper>