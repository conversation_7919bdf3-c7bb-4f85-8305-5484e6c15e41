{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\identity-select.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\identity-select.vue", "mtime": 1758268597206}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\babel.config.js", "mtime": 1758263450968}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758264043508}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758264043933}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiSWRlbnRpdHlTZWxlY3QiLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0aXRsZTogcHJvY2Vzcy5lbnYuVlVFX0FQUF9USVRMRQogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIHNlbGVjdElkZW50aXR5OiBmdW5jdGlvbiBzZWxlY3RJZGVudGl0eShpZGVudGl0eSkgewogICAgICAvLyDot7PovazliLDlr7nlupTouqvku73nmoTms6jlhozpobXpnaIKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6ICcvcmVnaXN0ZXInLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICB0eXBlOiBpZGVudGl0eQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgZ29Ub0xvZ2luOiBmdW5jdGlvbiBnb1RvTG9naW4oKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvbG9naW4nKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["name", "data", "title", "process", "env", "VUE_APP_TITLE", "methods", "selectIdentity", "identity", "$router", "push", "path", "query", "type", "goToLogin"], "sources": ["src/views/identity-select.vue"], "sourcesContent": ["<template>\r\n  <div class=\"identity-select\">\r\n    <div class=\"container\">\r\n      <div class=\"header\">\r\n        <h1 class=\"title\">{{ title }}</h1>\r\n        <p class=\"subtitle\">请选择您的身份类型</p>\r\n      </div>\r\n      \r\n      <div class=\"cards-container\">\r\n        <!-- 学生卡片 -->\r\n        <div class=\"identity-card student-card\" @click=\"selectIdentity('student')\">\r\n          <div class=\"card-icon\">\r\n            <svg-icon icon-class=\"education\" class=\"icon\" />\r\n          </div>\r\n          <div class=\"card-content\">\r\n            <h3 class=\"card-title\">学生</h3>\r\n            <p class=\"card-description\">学习课程、提交作业、查看学习进度</p>\r\n            <div class=\"card-features\">\r\n              <span class=\"feature-tag\">在线学习</span>\r\n              <span class=\"feature-tag\">作业提交</span>\r\n              <span class=\"feature-tag\">成绩查询</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"card-footer\">\r\n            <el-button type=\"primary\" class=\"action-btn\">开始学习</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 家长卡片 -->\r\n        <div class=\"identity-card parent-card\" @click=\"selectIdentity('parent')\">\r\n          <div class=\"card-icon\">\r\n            <svg-icon icon-class=\"peoples\" class=\"icon\" />\r\n          </div>\r\n          <div class=\"card-content\">\r\n            <h3 class=\"card-title\">家长</h3>\r\n            <p class=\"card-description\">管理孩子学习、监控学习进度、接收通知</p>\r\n            <div class=\"card-features\">\r\n              <span class=\"feature-tag\">学习监控</span>\r\n              <span class=\"feature-tag\">进度跟踪</span>\r\n              <span class=\"feature-tag\">消息通知</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"card-footer\">\r\n            <el-button type=\"success\" class=\"action-btn\">管理孩子</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 教师卡片 -->\r\n        <div class=\"identity-card teacher-card\" @click=\"selectIdentity('teacher')\">\r\n          <div class=\"card-icon\">\r\n            <svg-icon icon-class=\"user\" class=\"icon\" />\r\n          </div>\r\n          <div class=\"card-content\">\r\n            <h3 class=\"card-title\">教师</h3>\r\n            <p class=\"card-description\">管理学生、布置作业、批改作业、查看成绩</p>\r\n            <div class=\"card-features\">\r\n              <span class=\"feature-tag\">学生管理</span>\r\n              <span class=\"feature-tag\">作业批改</span>\r\n              <span class=\"feature-tag\">成绩统计</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"card-footer\">\r\n            <el-button type=\"warning\" class=\"action-btn\">开始教学</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"footer-actions\">\r\n        <p class=\"login-tip\">已有账号？</p>\r\n        <el-button type=\"text\" class=\"login-link\" @click=\"goToLogin\">直接登录</el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"IdentitySelect\",\r\n  data() {\r\n    return {\r\n      title: process.env.VUE_APP_TITLE\r\n    }\r\n  },\r\n  methods: {\r\n    selectIdentity(identity) {\r\n      // 跳转到对应身份的注册页面\r\n      this.$router.push({\r\n        path: '/register',\r\n        query: { type: identity }\r\n      })\r\n    },\r\n    goToLogin() {\r\n      this.$router.push('/login')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\r\n.identity-select {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px;\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: url('../assets/images/login-background.jpg') center/cover;\r\n    opacity: 0.1;\r\n    z-index: 0;\r\n  }\r\n\r\n  // 添加动态背景粒子效果\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-image: \r\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\r\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\r\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);\r\n    animation: backgroundShift 10s ease-in-out infinite;\r\n    z-index: 0;\r\n  }\r\n\r\n  .container {\r\n    position: relative;\r\n    z-index: 1;\r\n    max-width: 1200px;\r\n    width: 100%;\r\n  }\r\n\r\n  .header {\r\n    text-align: center;\r\n    margin-bottom: 60px;\r\n    \r\n    .title {\r\n      font-size: 3rem;\r\n      font-weight: 700;\r\n      color: #fff;\r\n      margin: 0 0 20px 0;\r\n      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n      animation: fadeInDown 1s ease-out;\r\n      background: linear-gradient(45deg, #fff, #f0f8ff);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      background-clip: text;\r\n    }\r\n    \r\n    .subtitle {\r\n      font-size: 1.2rem;\r\n      color: rgba(255, 255, 255, 0.9);\r\n      margin: 0;\r\n      animation: fadeInUp 1s ease-out 0.3s both;\r\n    }\r\n  }\r\n\r\n  .cards-container {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\r\n    gap: 40px;\r\n    margin-bottom: 40px;\r\n    perspective: 1000px;\r\n  }\r\n\r\n  .identity-card {\r\n    background: rgba(255, 255, 255, 0.95);\r\n    border-radius: 24px;\r\n    padding: 45px 35px;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n    box-shadow: \r\n      0 15px 35px rgba(0, 0, 0, 0.1),\r\n      0 5px 15px rgba(0, 0, 0, 0.08);\r\n    position: relative;\r\n    overflow: hidden;\r\n    animation: fadeInUp 1s ease-out;\r\n    backdrop-filter: blur(10px);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n\r\n    &:nth-child(1) { animation-delay: 0.1s; }\r\n    &:nth-child(2) { animation-delay: 0.2s; }\r\n    &:nth-child(3) { animation-delay: 0.3s; }\r\n\r\n    // 光泽效果\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: -100%;\r\n      width: 100%;\r\n      height: 100%;\r\n      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\r\n      transition: left 0.6s ease;\r\n    }\r\n\r\n    // 边框光效\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      border-radius: 24px;\r\n      padding: 2px;\r\n      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\r\n      mask-composite: exclude;\r\n      opacity: 0;\r\n      transition: opacity 0.3s ease;\r\n    }\r\n\r\n    &:hover {\r\n      transform: translateY(-20px) rotateX(5deg) rotateY(5deg) scale(1.05);\r\n      box-shadow: \r\n        0 30px 60px rgba(0, 0, 0, 0.25),\r\n        0 15px 30px rgba(0, 0, 0, 0.15),\r\n        inset 0 1px 0 rgba(255, 255, 255, 0.6);\r\n\r\n      &::before {\r\n        left: 100%;\r\n      }\r\n\r\n      &::after {\r\n        opacity: 1;\r\n      }\r\n\r\n      .card-icon {\r\n        transform: translateY(-10px);\r\n        \r\n        .icon {\r\n          transform: scale(1.2) rotate(10deg);\r\n        }\r\n      }\r\n\r\n      .card-content {\r\n        .card-title {\r\n          transform: translateY(-5px);\r\n        }\r\n\r\n        .feature-tag {\r\n          transform: translateY(-3px);\r\n          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n        }\r\n      }\r\n\r\n      .action-btn {\r\n        transform: translateY(-5px) scale(1.05);\r\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\r\n      }\r\n    }\r\n\r\n    .card-icon {\r\n      margin-bottom: 30px;\r\n      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n      \r\n      .icon {\r\n        font-size: 4.5rem;\r\n        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));\r\n      }\r\n    }\r\n\r\n    .card-content {\r\n      margin-bottom: 35px;\r\n\r\n      .card-title {\r\n        font-size: 2rem;\r\n        font-weight: 700;\r\n        margin: 0 0 15px 0;\r\n        color: #333;\r\n        transition: all 0.3s ease;\r\n        background: linear-gradient(45deg, #333, #666);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n      }\r\n\r\n      .card-description {\r\n        font-size: 1.1rem;\r\n        color: #666;\r\n        line-height: 1.7;\r\n        margin: 0 0 25px 0;\r\n        font-weight: 400;\r\n      }\r\n\r\n      .card-features {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        justify-content: center;\r\n        gap: 12px;\r\n\r\n        .feature-tag {\r\n          background: #f8f9fa;\r\n          color: #495057;\r\n          padding: 8px 16px;\r\n          border-radius: 25px;\r\n          font-size: 0.9rem;\r\n          font-weight: 500;\r\n          transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n          border: 1px solid rgba(0, 0, 0, 0.05);\r\n          backdrop-filter: blur(5px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .card-footer {\r\n      .action-btn {\r\n        padding: 15px 35px;\r\n        border-radius: 30px;\r\n        font-weight: 700;\r\n        font-size: 1.1rem;\r\n        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n        border: none;\r\n        min-width: 160px;\r\n        position: relative;\r\n        overflow: hidden;\r\n\r\n        &::before {\r\n          content: '';\r\n          position: absolute;\r\n          top: 0;\r\n          left: -100%;\r\n          width: 100%;\r\n          height: 100%;\r\n          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n          transition: left 0.5s ease;\r\n        }\r\n\r\n        &:hover::before {\r\n          left: 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 不同身份卡片的特殊样式\r\n    &.student-card {\r\n      .card-icon .icon {\r\n        color: #409EFF;\r\n        background: linear-gradient(135deg, #409EFF, #66b1ff);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n      }\r\n      \r\n      .feature-tag {\r\n        background: linear-gradient(135deg, #e6f7ff, #f0f9ff);\r\n        color: #1890ff;\r\n        border-color: rgba(24, 144, 255, 0.2);\r\n      }\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(230, 247, 255, 0.95));\r\n      }\r\n    }\r\n\r\n    &.parent-card {\r\n      .card-icon .icon {\r\n        color: #67C23A;\r\n        background: linear-gradient(135deg, #67C23A, #85ce61);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n      }\r\n      \r\n      .feature-tag {\r\n        background: linear-gradient(135deg, #f6ffed, #f9fff6);\r\n        color: #52c41a;\r\n        border-color: rgba(82, 196, 26, 0.2);\r\n      }\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(246, 255, 237, 0.95));\r\n      }\r\n    }\r\n\r\n    &.teacher-card {\r\n      .card-icon .icon {\r\n        color: #E6A23C;\r\n        background: linear-gradient(135deg, #E6A23C, #f0c78a);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n      }\r\n      \r\n      .feature-tag {\r\n        background: linear-gradient(135deg, #fffbe6, #fffef0);\r\n        color: #faad14;\r\n        border-color: rgba(250, 173, 20, 0.2);\r\n      }\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(255, 251, 230, 0.95));\r\n      }\r\n    }\r\n  }\r\n\r\n  .footer-actions {\r\n    text-align: center;\r\n    animation: fadeInUp 1s ease-out 0.6s both;\r\n\r\n    .login-tip {\r\n      color: rgba(255, 255, 255, 0.8);\r\n      margin: 0 10px 0 0;\r\n      display: inline-block;\r\n      font-size: 1.1rem;\r\n    }\r\n\r\n    .login-link {\r\n      color: #fff;\r\n      font-size: 1.2rem;\r\n      font-weight: 600;\r\n      text-decoration: none;\r\n      transition: all 0.3s ease;\r\n      padding: 8px 16px;\r\n      border-radius: 20px;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      backdrop-filter: blur(5px);\r\n\r\n      &:hover {\r\n        color: #ffd700;\r\n        text-shadow: 0 0 15px rgba(255, 215, 0, 0.6);\r\n        background: rgba(255, 255, 255, 0.2);\r\n        transform: translateY(-2px);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 动画定义\r\n@keyframes fadeInDown {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes backgroundShift {\r\n  0%, 100% {\r\n    transform: translateX(0) translateY(0);\r\n  }\r\n  25% {\r\n    transform: translateX(-20px) translateY(-10px);\r\n  }\r\n  50% {\r\n    transform: translateX(20px) translateY(10px);\r\n  }\r\n  75% {\r\n    transform: translateX(-10px) translateY(20px);\r\n  }\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-10px);\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.05);\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 1200px) {\r\n  .identity-select {\r\n    .cards-container {\r\n      gap: 30px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .identity-select {\r\n    padding: 15px;\r\n\r\n    .header {\r\n      margin-bottom: 40px;\r\n      \r\n      .title {\r\n        font-size: 2.2rem;\r\n      }\r\n      \r\n      .subtitle {\r\n        font-size: 1.1rem;\r\n      }\r\n    }\r\n    \r\n    .cards-container {\r\n      grid-template-columns: 1fr;\r\n      gap: 25px;\r\n    }\r\n    \r\n    .identity-card {\r\n      padding: 35px 25px;\r\n      border-radius: 20px;\r\n\r\n      &:hover {\r\n        transform: translateY(-15px) scale(1.02);\r\n      }\r\n\r\n      .card-icon .icon {\r\n        font-size: 3.5rem;\r\n      }\r\n\r\n      .card-content {\r\n        .card-title {\r\n          font-size: 1.6rem;\r\n        }\r\n\r\n        .card-description {\r\n          font-size: 1rem;\r\n        }\r\n\r\n        .card-features {\r\n          gap: 8px;\r\n\r\n          .feature-tag {\r\n            font-size: 0.85rem;\r\n            padding: 6px 12px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .card-footer .action-btn {\r\n        padding: 12px 28px;\r\n        font-size: 1rem;\r\n        min-width: 140px;\r\n      }\r\n    }\r\n\r\n    .footer-actions {\r\n      .login-tip {\r\n        font-size: 1rem;\r\n      }\r\n\r\n      .login-link {\r\n        font-size: 1.1rem;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .identity-select {\r\n    padding: 10px;\r\n\r\n    .header {\r\n      margin-bottom: 30px;\r\n      \r\n      .title {\r\n        font-size: 1.8rem;\r\n      }\r\n      \r\n      .subtitle {\r\n        font-size: 1rem;\r\n      }\r\n    }\r\n    \r\n    .cards-container {\r\n      gap: 20px;\r\n    }\r\n    \r\n    .identity-card {\r\n      padding: 30px 20px;\r\n\r\n      .card-icon .icon {\r\n        font-size: 3rem;\r\n      }\r\n\r\n      .card-content {\r\n        margin-bottom: 25px;\r\n\r\n        .card-title {\r\n          font-size: 1.4rem;\r\n        }\r\n\r\n        .card-description {\r\n          font-size: 0.95rem;\r\n        }\r\n\r\n        .card-features {\r\n          .feature-tag {\r\n            font-size: 0.8rem;\r\n            padding: 5px 10px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .card-footer .action-btn {\r\n        padding: 10px 24px;\r\n        font-size: 0.95rem;\r\n        min-width: 120px;\r\n      }\r\n    }\r\n\r\n    .footer-actions {\r\n      .login-tip, .login-link {\r\n        font-size: 0.95rem;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCA4EA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC;IACA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAC,QAAA;MACA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAC,KAAA;UAAAC,IAAA,EAAAL;QAAA;MACA;IACA;IACAM,SAAA,WAAAA,UAAA;MACA,KAAAL,OAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}