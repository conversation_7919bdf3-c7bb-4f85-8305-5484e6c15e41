package com.lingdu.system.mapper;

import java.util.List;
import com.lingdu.system.domain.parentInfo;

/**
 * 家长信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
public interface parentInfoMapper 
{
    /**
     * 查询家长信息
     * 
     * @param parentId 家长信息主键
     * @return 家长信息
     */
    public parentInfo selectparentInfoByParentId(Long parentId);

    /**
     * 查询家长信息列表
     * 
     * @param parentInfo 家长信息
     * @return 家长信息集合
     */
    public List<parentInfo> selectparentInfoList(parentInfo parentInfo);

    /**
     * 新增家长信息
     * 
     * @param parentInfo 家长信息
     * @return 结果
     */
    public int insertparentInfo(parentInfo parentInfo);

    /**
     * 修改家长信息
     * 
     * @param parentInfo 家长信息
     * @return 结果
     */
    public int updateparentInfo(parentInfo parentInfo);

    /**
     * 删除家长信息
     * 
     * @param parentId 家长信息主键
     * @return 结果
     */
    public int deleteparentInfoByParentId(Long parentId);

    /**
     * 批量删除家长信息
     * 
     * @param parentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteparentInfoByParentIds(Long[] parentIds);
}
