package com.lingdu.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lingdu.common.annotation.Log;
import com.lingdu.common.core.controller.BaseController;
import com.lingdu.common.core.domain.AjaxResult;
import com.lingdu.common.enums.BusinessType;
import com.lingdu.system.domain.UserParentStudent;
import com.lingdu.system.service.IUserParentStudentService;
import com.lingdu.common.utils.poi.ExcelUtil;
import com.lingdu.common.core.page.TableDataInfo;

/**
 * 家长与学生绑定关系Controller
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
@RestController
@RequestMapping("/user/UserParentStudent")
public class UserParentStudentController extends BaseController
{
    @Autowired
    private IUserParentStudentService userParentStudentService;

    /**
     * 查询家长与学生绑定关系列表
     */
    @PreAuthorize("@ss.hasPermi('user:UserParentStudent:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserParentStudent userParentStudent)
    {
        startPage();
        List<UserParentStudent> list = userParentStudentService.selectUserParentStudentList(userParentStudent);
        return getDataTable(list);
    }

    /**
     * 导出家长与学生绑定关系列表
     */
    @PreAuthorize("@ss.hasPermi('user:UserParentStudent:export')")
    @Log(title = "家长与学生绑定关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserParentStudent userParentStudent)
    {
        List<UserParentStudent> list = userParentStudentService.selectUserParentStudentList(userParentStudent);
        ExcelUtil<UserParentStudent> util = new ExcelUtil<UserParentStudent>(UserParentStudent.class);
        util.exportExcel(response, list, "家长与学生绑定关系数据");
    }

    /**
     * 获取家长与学生绑定关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('user:UserParentStudent:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(userParentStudentService.selectUserParentStudentById(id));
    }

    /**
     * 新增家长与学生绑定关系
     */
    @PreAuthorize("@ss.hasPermi('user:UserParentStudent:add')")
    @Log(title = "家长与学生绑定关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserParentStudent userParentStudent)
    {
        return toAjax(userParentStudentService.insertUserParentStudent(userParentStudent));
    }

    /**
     * 修改家长与学生绑定关系
     */
    @PreAuthorize("@ss.hasPermi('user:UserParentStudent:edit')")
    @Log(title = "家长与学生绑定关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserParentStudent userParentStudent)
    {
        return toAjax(userParentStudentService.updateUserParentStudent(userParentStudent));
    }

    /**
     * 删除家长与学生绑定关系
     */
    @PreAuthorize("@ss.hasPermi('user:UserParentStudent:remove')")
    @Log(title = "家长与学生绑定关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(userParentStudentService.deleteUserParentStudentByIds(ids));
    }
}
