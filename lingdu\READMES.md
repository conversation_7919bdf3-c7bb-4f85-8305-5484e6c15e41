CREATE TABLE student_info (
student_id      BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '学生ID',
user_id         BIGINT NOT NULL COMMENT '关联sys_user表的用户ID',
grade           VARCHAR(20) NOT NULL COMMENT '年级，例如：小学三年级',
stage           VARCHAR(20) NOT NULL COMMENT '学段，例如：小学/初中/高中',
main_subject    VARCHAR(20) NOT NULL COMMENT '主学科，例如：数学',
extra_subjects  VARCHAR(100) DEFAULT NULL COMMENT '副学科，逗号分隔',
membership_type VARCHAR(20) DEFAULT 'free' COMMENT '会员类型：free/trial/vip',
create_time     DATETIME DEFAULT CURRENT_TIMESTAMP,
update_time     DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生信息表';


CREATE TABLE parent_info (
parent_id   BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '家长ID',
user_id     BIGINT NOT NULL COMMENT '关联sys_user表的用户ID',
phone       VARCHAR(20) NOT NULL COMMENT '手机号',
name        VARCHAR(50) NOT NULL COMMENT '姓名',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='家长信息表';


CREATE TABLE user_parent_student (
id         BIGINT PRIMARY KEY AUTO_INCREMENT,
parent_id  BIGINT NOT NULL COMMENT '家长ID',
student_id BIGINT NOT NULL COMMENT '学生ID',
relation   VARCHAR(20) DEFAULT NULL COMMENT '关系：父亲/母亲/监护人',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
UNIQUE KEY uk_parent_student (parent_id, student_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='家长与学生绑定关系表';



CREATE TABLE teacher_info (
teacher_id      BIGINT PRIMARY KEY AUTO_INCREMENT,
user_id         BIGINT NOT NULL COMMENT '关联sys_user表的用户ID',
phone           VARCHAR(20) NOT NULL,
name            VARCHAR(50) NOT NULL,
certificate_no  VARCHAR(50) NOT NULL COMMENT '教师资格证编号',
verify_status   TINYINT DEFAULT 0 COMMENT '审核状态：0待审核 1已通过 2已拒绝',
stages          VARCHAR(100) DEFAULT NULL COMMENT '教授学段，逗号分隔',
subjects        VARCHAR(100) DEFAULT NULL COMMENT '教授学科，逗号分隔',
create_time     DATETIME DEFAULT CURRENT_TIMESTAMP,
update_time     DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师信息表';



CREATE TABLE course (
course_id   BIGINT PRIMARY KEY AUTO_INCREMENT,
title       VARCHAR(100) NOT NULL COMMENT '课程标题',
subject     VARCHAR(20) NOT NULL COMMENT '科目',
grade       VARCHAR(20) NOT NULL COMMENT '年级',
teacher_id  BIGINT NOT NULL COMMENT '授课老师ID',
resource_url VARCHAR(255) DEFAULT NULL COMMENT '资源路径/文件地址',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程信息表';


CREATE TABLE course_plan (
plan_id     BIGINT PRIMARY KEY AUTO_INCREMENT,
title       VARCHAR(100) NOT NULL,
student_id  BIGINT NOT NULL,
course_id   BIGINT DEFAULT NULL,
deadline    DATETIME DEFAULT NULL,
status      TINYINT DEFAULT 0 COMMENT '0未开始 1进行中 2已完成 3逾期',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学习计划表';



CREATE TABLE mistake_book (
mistake_id  BIGINT PRIMARY KEY AUTO_INCREMENT,
student_id  BIGINT NOT NULL,
question    TEXT NOT NULL COMMENT '题干',
wrong_answer TEXT NOT NULL COMMENT '学生错误答案',
correct_answer TEXT DEFAULT NULL COMMENT '正确答案',
analysis    TEXT DEFAULT NULL COMMENT '题目解析',
knowledge_point VARCHAR(100) DEFAULT NULL COMMENT '知识点',
status      TINYINT DEFAULT 0 COMMENT '0未复盘 1已复盘',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='错题本';



CREATE TABLE membership (
membership_id BIGINT PRIMARY KEY AUTO_INCREMENT,
name          VARCHAR(50) NOT NULL COMMENT '套餐名称',
type          VARCHAR(20) NOT NULL COMMENT '类型：trial/vip',
price         DECIMAL(10,2) NOT NULL,
duration_days INT NOT NULL COMMENT '有效天数',
auto_renew    TINYINT DEFAULT 0 COMMENT '是否自动续费：0否 1是',
create_time   DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员套餐';



CREATE TABLE student_membership (
id            BIGINT PRIMARY KEY AUTO_INCREMENT,
student_id    BIGINT NOT NULL,
membership_id BIGINT NOT NULL,
start_time    DATETIME NOT NULL,
end_time      DATETIME NOT NULL,
auto_renew    TINYINT DEFAULT 0,
create_time   DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生订阅会员表';


CREATE TABLE order_info (
order_id    BIGINT PRIMARY KEY AUTO_INCREMENT,
order_no    VARCHAR(64) NOT NULL COMMENT '唯一订单号',
student_id  BIGINT NOT NULL,
membership_id BIGINT NOT NULL,
amount      DECIMAL(10,2) NOT NULL,
pay_status  TINYINT DEFAULT 0 COMMENT '0待支付 1支付成功 2支付失败',
pay_method  VARCHAR(20) DEFAULT 'wechat' COMMENT '支付方式',
pay_time    DATETIME DEFAULT NULL,
create_time DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付订单表';



CREATE TABLE teacher_class (
class_id    BIGINT PRIMARY KEY AUTO_INCREMENT,
teacher_id  BIGINT NOT NULL COMMENT '教师ID',
class_name  VARCHAR(50) NOT NULL COMMENT '班级名称，例如：好/中/差',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师班级表';


CREATE TABLE teacher_student (
id          BIGINT PRIMARY KEY AUTO_INCREMENT,
teacher_id  BIGINT NOT NULL,
student_id  BIGINT NOT NULL,
class_id    BIGINT DEFAULT NULL COMMENT '所属班级',
tag         VARCHAR(50) DEFAULT NULL COMMENT '学生标签',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
UNIQUE KEY uk_teacher_student (teacher_id, student_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师-学生关系表';



CREATE TABLE teacher_material (
material_id BIGINT PRIMARY KEY AUTO_INCREMENT,
teacher_id  BIGINT NOT NULL,
title       VARCHAR(100) NOT NULL,
file_url    VARCHAR(255) NOT NULL COMMENT '文件存储路径',
type        VARCHAR(20) DEFAULT 'file' COMMENT 'file/doc/video',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师资料表';



CREATE TABLE teacher_salary (
salary_id   BIGINT PRIMARY KEY AUTO_INCREMENT,
teacher_id  BIGINT NOT NULL,
month       VARCHAR(20) NOT NULL COMMENT '月份 例如：2025-09',
base_salary DECIMAL(10,2) NOT NULL COMMENT '基础工资',
bonus       DECIMAL(10,2) DEFAULT 0 COMMENT '奖励',
penalty     DECIMAL(10,2) DEFAULT 0 COMMENT '惩罚',
total       DECIMAL(10,2) NOT NULL COMMENT '实际工资',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师工资表';



CREATE TABLE parent_control (
control_id  BIGINT PRIMARY KEY AUTO_INCREMENT,
parent_id   BIGINT NOT NULL,
student_id  BIGINT NOT NULL,
allowed_time VARCHAR(100) DEFAULT NULL COMMENT '可用时段 例如：08:00-21:00',
max_duration INT DEFAULT NULL COMMENT '单次最长使用分钟数',
rest_interval INT DEFAULT NULL COMMENT '休息间隔分钟数',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='家长管控表';



CREATE TABLE parent_teacher_chat (
chat_id     BIGINT PRIMARY KEY AUTO_INCREMENT,
parent_id   BIGINT NOT NULL,
teacher_id  BIGINT NOT NULL,
sender_type VARCHAR(20) NOT NULL COMMENT 'sender: parent/teacher',
content     TEXT NOT NULL,
status      TINYINT DEFAULT 0 COMMENT '0未读 1已读',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='家长与老师对话表';



CREATE TABLE content_audit (
audit_id    BIGINT PRIMARY KEY AUTO_INCREMENT,
content_id  BIGINT NOT NULL,
content_type VARCHAR(20) NOT NULL COMMENT 'course/material/announcement',
status      TINYINT DEFAULT 0 COMMENT '0待审核 1通过 2拒绝',
auditor_id  BIGINT DEFAULT NULL COMMENT '审核管理员ID',
remark      VARCHAR(255) DEFAULT NULL,
create_time DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容审核表';



CREATE TABLE api_config (
api_id      BIGINT PRIMARY KEY AUTO_INCREMENT,
api_name    VARCHAR(100) NOT NULL,
params      TEXT DEFAULT NULL COMMENT '配置参数(JSON)',
status      TINYINT DEFAULT 1 COMMENT '0离线 1在线',
last_check  DATETIME DEFAULT NULL COMMENT '最后检测时间',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='接口配置表';


CREATE TABLE system_log (
log_id      BIGINT PRIMARY KEY AUTO_INCREMENT,
user_id     BIGINT DEFAULT NULL,
action      VARCHAR(100) NOT NULL COMMENT '操作名称',
detail      TEXT DEFAULT NULL,
ip_address  VARCHAR(50) DEFAULT NULL,
create_time DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统操作日志';


CREATE TABLE payment_record (
id          BIGINT PRIMARY KEY AUTO_INCREMENT,
order_id    BIGINT NOT NULL,
transaction_no VARCHAR(64) NOT NULL COMMENT '第三方支付流水号',
status      TINYINT DEFAULT 0 COMMENT '0待确认 1成功 2失败 3退款',
amount      DECIMAL(10,2) NOT NULL,
pay_time    DATETIME DEFAULT NULL,
create_time DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付流水表';