package com.lingdu.system.mapper;

import java.util.List;
import com.lingdu.system.domain.studentInfo;

/**
 * 学生信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
public interface studentInfoMapper 
{
    /**
     * 查询学生信息
     * 
     * @param studentId 学生信息主键
     * @return 学生信息
     */
    public studentInfo selectstudentInfoByStudentId(Long studentId);

    /**
     * 查询学生信息列表
     * 
     * @param studentInfo 学生信息
     * @return 学生信息集合
     */
    public List<studentInfo> selectstudentInfoList(studentInfo studentInfo);

    /**
     * 新增学生信息
     * 
     * @param studentInfo 学生信息
     * @return 结果
     */
    public int insertstudentInfo(studentInfo studentInfo);

    /**
     * 修改学生信息
     * 
     * @param studentInfo 学生信息
     * @return 结果
     */
    public int updatestudentInfo(studentInfo studentInfo);

    /**
     * 删除学生信息
     * 
     * @param studentId 学生信息主键
     * @return 结果
     */
    public int deletestudentInfoByStudentId(Long studentId);

    /**
     * 批量删除学生信息
     * 
     * @param studentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletestudentInfoByStudentIds(Long[] studentIds);
}
