package com.lingdu.system.service.impl;

import java.util.List;
import com.lingdu.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lingdu.system.mapper.TeacherInfoMapper;
import com.lingdu.system.domain.TeacherInfo;
import com.lingdu.system.service.ITeacherInfoService;

/**
 * 教师信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
@Service
public class TeacherInfoServiceImpl implements ITeacherInfoService 
{
    @Autowired
    private TeacherInfoMapper teacherInfoMapper;

    /**
     * 查询教师信息
     * 
     * @param teacherId 教师信息主键
     * @return 教师信息
     */
    @Override
    public TeacherInfo selectTeacherInfoByTeacherId(Long teacherId)
    {
        return teacherInfoMapper.selectTeacherInfoByTeacherId(teacherId);
    }

    /**
     * 查询教师信息列表
     * 
     * @param teacherInfo 教师信息
     * @return 教师信息
     */
    @Override
    public List<TeacherInfo> selectTeacherInfoList(TeacherInfo teacherInfo)
    {
        return teacherInfoMapper.selectTeacherInfoList(teacherInfo);
    }

    /**
     * 新增教师信息
     * 
     * @param teacherInfo 教师信息
     * @return 结果
     */
    @Override
    public int insertTeacherInfo(TeacherInfo teacherInfo)
    {
        teacherInfo.setCreateTime(DateUtils.getNowDate());
        return teacherInfoMapper.insertTeacherInfo(teacherInfo);
    }

    /**
     * 修改教师信息
     * 
     * @param teacherInfo 教师信息
     * @return 结果
     */
    @Override
    public int updateTeacherInfo(TeacherInfo teacherInfo)
    {
        teacherInfo.setUpdateTime(DateUtils.getNowDate());
        return teacherInfoMapper.updateTeacherInfo(teacherInfo);
    }

    /**
     * 批量删除教师信息
     * 
     * @param teacherIds 需要删除的教师信息主键
     * @return 结果
     */
    @Override
    public int deleteTeacherInfoByTeacherIds(Long[] teacherIds)
    {
        return teacherInfoMapper.deleteTeacherInfoByTeacherIds(teacherIds);
    }

    /**
     * 删除教师信息信息
     * 
     * @param teacherId 教师信息主键
     * @return 结果
     */
    @Override
    public int deleteTeacherInfoByTeacherId(Long teacherId)
    {
        return teacherInfoMapper.deleteTeacherInfoByTeacherId(teacherId);
    }
}
