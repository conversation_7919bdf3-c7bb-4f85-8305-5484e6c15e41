import request from '@/utils/request'

// 查询学生信息列表
export function listStudentInfo(query) {
  return request({
    url: '/user/studentInfo/list',
    method: 'get',
    params: query
  })
}

// 查询学生信息详细
export function getStudentInfo(studentId) {
  return request({
    url: '/user/studentInfo/' + studentId,
    method: 'get'
  })
}

// 新增学生信息
export function addStudentInfo(data) {
  return request({
    url: '/user/studentInfo',
    method: 'post',
    data: data
  })
}

// 修改学生信息
export function updateStudentInfo(data) {
  return request({
    url: '/user/studentInfo',
    method: 'put',
    data: data
  })
}

// 删除学生信息
export function delStudentInfo(studentId) {
  return request({
    url: '/user/studentInfo/' + studentId,
    method: 'delete'
  })
}
