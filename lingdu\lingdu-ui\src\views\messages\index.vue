<template>
    <div class="messages-page">
        <el-card class="header-card" shadow="never">
            <div class="header-content">
                <h1 class="page-title">
                    <i class="el-icon-message"></i>
                    消息中心
                </h1>
                <p class="page-subtitle">与老师沟通学习情况，及时了解孩子学习动态</p>
            </div>
        </el-card>

        <div class="content">
            <el-row :gutter="24">
                <!-- 消息列表 -->
                <el-col :span="16">
                    <el-card class="message-list-card" shadow="hover">
                        <div slot="header" class="card-header">
                            <span>消息列表</span>
                            <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="unread-badge">
                                <el-button size="small" @click="markAllRead">全部已读</el-button>
                            </el-badge>
                        </div>

                        <div class="message-list">
                            <div v-for="message in messages" :key="message.id" class="message-item"
                                :class="{ 'unread': !message.isRead }" @click="selectMessage(message)">
                                <div class="message-avatar">
                                    <i class="el-icon-user-solid"></i>
                                </div>
                                <div class="message-content">
                                    <div class="message-header">
                                        <span class="sender-name">{{ message.senderName }}</span>
                                        <span class="message-time">{{ formatTime(message.createTime) }}</span>
                                        <el-tag v-if="!message.isRead" type="danger" size="mini">未读</el-tag>
                                    </div>
                                    <div class="message-preview">{{ message.content }}</div>
                                    <div class="message-meta">
                                        <span class="subject">{{ message.subject }}</span>
                                        <span class="grade">{{ message.grade }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </el-col>

                <!-- 消息详情和回复 -->
                <el-col :span="8">
                    <el-card class="message-detail-card" shadow="hover">
                        <div slot="header">
                            <span>消息详情</span>
                        </div>

                        <div v-if="selectedMessage" class="message-detail">
                            <div class="detail-header">
                                <h3>{{ selectedMessage.subject }}</h3>
                                <p class="detail-meta">
                                    <span>发送人：{{ selectedMessage.senderName }}</span>
                                    <span>时间：{{ formatTime(selectedMessage.createTime) }}</span>
                                </p>
                            </div>

                            <div class="detail-content">
                                <p>{{ selectedMessage.content }}</p>
                            </div>

                            <div class="reply-section">
                                <h4>回复消息</h4>
                                <el-form :model="replyForm" @submit.native.prevent="sendReply">
                                    <el-form-item>
                                        <el-input v-model="replyForm.content" type="textarea" :rows="4"
                                            placeholder="请输入回复内容..."></el-input>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="sendReply" :loading="sending">
                                            发送回复
                                        </el-button>
                                    </el-form-item>
                                </el-form>
                            </div>
                        </div>

                        <div v-else class="no-selection">
                            <i class="el-icon-chat-line-round"></i>
                            <p>请选择一条消息查看详情</p>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <site-footer />
    </div>
</template>

<script>
import SiteFooter from '@/components/SiteFooter'

export default {
    name: 'MessagesPage',
    components: { SiteFooter },
    data() {
        return {
            selectedMessage: null,
            sending: false,
            replyForm: {
                content: ''
            },
            messages: [
                {
                    id: 1,
                    senderName: '张老师',
                    subject: '数学作业完成情况',
                    content: '小明同学，你的数学作业完成得很好，特别是几何题目的解题思路很清晰。建议继续保持，同时可以尝试一些更有挑战性的题目。',
                    grade: '小学三年级',
                    createTime: new Date('2024-01-15 14:30:00'),
                    isRead: false
                },
                {
                    id: 2,
                    senderName: '李老师',
                    subject: '英语口语练习建议',
                    content: '家长您好，小红的英语口语进步很大，建议在家多进行英语对话练习，可以看一些英语动画片提高语感。',
                    grade: '小学四年级',
                    createTime: new Date('2024-01-14 16:20:00'),
                    isRead: true
                },
                {
                    id: 3,
                    senderName: '王老师',
                    subject: '科学实验报告',
                    content: '小刚的科学实验报告写得非常详细，观察记录也很准确。建议下次可以尝试自己设计实验步骤。',
                    grade: '小学五年级',
                    createTime: new Date('2024-01-13 10:15:00'),
                    isRead: false
                },
                {
                    id: 4,
                    senderName: '陈老师',
                    subject: '语文作文指导',
                    content: '小丽的作文想象力丰富，语言表达也很生动。建议多阅读课外书籍，积累更多词汇和表达方式。',
                    grade: '小学六年级',
                    createTime: new Date('2024-01-12 09:45:00'),
                    isRead: true
                }
            ]
        }
    },
    computed: {
        unreadCount() {
            return this.messages.filter(msg => !msg.isRead).length
        }
    },
    methods: {
        selectMessage(message) {
            this.selectedMessage = message
            if (!message.isRead) {
                message.isRead = true
            }
        },
        markAllRead() {
            this.messages.forEach(msg => {
                msg.isRead = true
            })
            this.$message.success('所有消息已标记为已读')
        },
        sendReply() {
            if (!this.replyForm.content.trim()) {
                this.$message.warning('请输入回复内容')
                return
            }

            this.sending = true
            setTimeout(() => {
                this.sending = false
                this.$message.success('回复发送成功')
                this.replyForm.content = ''
            }, 1000)
        },
        formatTime(time) {
            const now = new Date()
            const diff = now - time
            const days = Math.floor(diff / (1000 * 60 * 60 * 24))

            if (days === 0) {
                return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
            } else if (days === 1) {
                return '昨天 ' + time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
            } else if (days < 7) {
                return days + '天前'
            } else {
                return time.toLocaleDateString('zh-CN')
            }
        }
    }
}
</script>

<style scoped>
.messages-page {
    padding: 20px;
    min-height: 100vh;
    background: #f5f7fa;
}

.header-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-bottom: 20px;
    border: none;
}

.header-content {
    text-align: center;
    padding: 20px;
}

.page-title {
    margin: 0 0 8px;
    font-size: 28px;
    font-weight: 700;
}

.page-title i {
    margin-right: 8px;
}

.page-subtitle {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

.content {
    max-width: 1200px;
    margin: 0 auto;
}

.message-list-card,
.message-detail-card {
    height: 600px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message-list {
    height: 520px;
    overflow-y: auto;
}

.message-item {
    display: flex;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.3s;
}

.message-item:hover {
    background-color: #f8f9fa;
}

.message-item.unread {
    background-color: #f0f9ff;
    border-left: 3px solid #409EFF;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #409EFF;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.sender-name {
    font-weight: 600;
    color: #303133;
}

.message-time {
    font-size: 12px;
    color: #909399;
}

.message-preview {
    color: #606266;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.message-meta {
    display: flex;
    gap: 8px;
}

.subject,
.grade {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
    background: #f0f0f0;
    color: #666;
}

.message-detail {
    height: 520px;
    display: flex;
    flex-direction: column;
}

.detail-header {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 16px;
    margin-bottom: 16px;
}

.detail-header h3 {
    margin: 0 0 8px;
    color: #303133;
}

.detail-meta {
    margin: 0;
    font-size: 12px;
    color: #909399;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-content {
    flex: 1;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;
}

.detail-content p {
    margin: 0;
    line-height: 1.6;
    color: #606266;
}

.reply-section h4 {
    margin: 0 0 12px;
    color: #303133;
    font-size: 14px;
}

.no-selection {
    height: 520px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;
}

.no-selection i {
    font-size: 48px;
    margin-bottom: 16px;
}

.unread-badge {
    margin-left: 8px;
}
</style>