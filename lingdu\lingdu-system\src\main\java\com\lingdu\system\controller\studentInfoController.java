package com.lingdu.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lingdu.common.annotation.Log;
import com.lingdu.common.core.controller.BaseController;
import com.lingdu.common.core.domain.AjaxResult;
import com.lingdu.common.enums.BusinessType;
import com.lingdu.system.domain.studentInfo;
import com.lingdu.system.service.IstudentInfoService;
import com.lingdu.common.utils.poi.ExcelUtil;
import com.lingdu.common.core.page.TableDataInfo;

/**
 * 学生信息Controller
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
@RestController
@RequestMapping("/user/studentInfo")
public class studentInfoController extends BaseController
{
    @Autowired
    private IstudentInfoService studentInfoService;

    /**
     * 查询学生信息列表
     */
    @PreAuthorize("@ss.hasPermi('user:studentInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(studentInfo studentInfo)
    {
        startPage();
        List<studentInfo> list = studentInfoService.selectstudentInfoList(studentInfo);
        return getDataTable(list);
    }

    /**
     * 导出学生信息列表
     */
    @PreAuthorize("@ss.hasPermi('user:studentInfo:export')")
    @Log(title = "学生信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, studentInfo studentInfo)
    {
        List<studentInfo> list = studentInfoService.selectstudentInfoList(studentInfo);
        ExcelUtil<studentInfo> util = new ExcelUtil<studentInfo>(studentInfo.class);
        util.exportExcel(response, list, "学生信息数据");
    }

    /**
     * 获取学生信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('user:studentInfo:query')")
    @GetMapping(value = "/{studentId}")
    public AjaxResult getInfo(@PathVariable("studentId") Long studentId)
    {
        return success(studentInfoService.selectstudentInfoByStudentId(studentId));
    }

    /**
     * 新增学生信息
     */
    @PreAuthorize("@ss.hasPermi('user:studentInfo:add')")
    @Log(title = "学生信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody studentInfo studentInfo)
    {
        return toAjax(studentInfoService.insertstudentInfo(studentInfo));
    }

    /**
     * 修改学生信息
     */
    @PreAuthorize("@ss.hasPermi('user:studentInfo:edit')")
    @Log(title = "学生信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody studentInfo studentInfo)
    {
        return toAjax(studentInfoService.updatestudentInfo(studentInfo));
    }

    /**
     * 删除学生信息
     */
    @PreAuthorize("@ss.hasPermi('user:studentInfo:remove')")
    @Log(title = "学生信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{studentIds}")
    public AjaxResult remove(@PathVariable Long[] studentIds)
    {
        return toAjax(studentInfoService.deletestudentInfoByStudentIds(studentIds));
    }
}
