package com.lingdu.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lingdu.common.annotation.Log;
import com.lingdu.common.core.controller.BaseController;
import com.lingdu.common.core.domain.AjaxResult;
import com.lingdu.common.enums.BusinessType;
import com.lingdu.system.domain.parentInfo;
import com.lingdu.system.service.IparentInfoService;
import com.lingdu.common.utils.poi.ExcelUtil;
import com.lingdu.common.core.page.TableDataInfo;

/**
 * 家长信息Controller
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
@RestController
@RequestMapping("/user/parentinfo")
public class parentInfoController extends BaseController
{
    @Autowired
    private IparentInfoService parentInfoService;

    /**
     * 查询家长信息列表
     */
    @PreAuthorize("@ss.hasPermi('user:parentinfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(parentInfo parentInfo)
    {
        startPage();
        List<parentInfo> list = parentInfoService.selectparentInfoList(parentInfo);
        return getDataTable(list);
    }

    /**
     * 导出家长信息列表
     */
    @PreAuthorize("@ss.hasPermi('user:parentinfo:export')")
    @Log(title = "家长信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, parentInfo parentInfo)
    {
        List<parentInfo> list = parentInfoService.selectparentInfoList(parentInfo);
        ExcelUtil<parentInfo> util = new ExcelUtil<parentInfo>(parentInfo.class);
        util.exportExcel(response, list, "家长信息数据");
    }

    /**
     * 获取家长信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('user:parentinfo:query')")
    @GetMapping(value = "/{parentId}")
    public AjaxResult getInfo(@PathVariable("parentId") Long parentId)
    {
        return success(parentInfoService.selectparentInfoByParentId(parentId));
    }

    /**
     * 新增家长信息
     */
    @PreAuthorize("@ss.hasPermi('user:parentinfo:add')")
    @Log(title = "家长信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody parentInfo parentInfo)
    {
        return toAjax(parentInfoService.insertparentInfo(parentInfo));
    }

    /**
     * 修改家长信息
     */
    @PreAuthorize("@ss.hasPermi('user:parentinfo:edit')")
    @Log(title = "家长信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody parentInfo parentInfo)
    {
        return toAjax(parentInfoService.updateparentInfo(parentInfo));
    }

    /**
     * 删除家长信息
     */
    @PreAuthorize("@ss.hasPermi('user:parentinfo:remove')")
    @Log(title = "家长信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{parentIds}")
    public AjaxResult remove(@PathVariable Long[] parentIds)
    {
        return toAjax(parentInfoService.deleteparentInfoByParentIds(parentIds));
    }
}
