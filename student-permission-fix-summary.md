# 学生权限问题修复总结

## 🐛 问题描述
学生身份注册后登录前端只能看到一个模块，权限分配不正确。

## 🔍 问题原因分析

### 1. 角色ID不匹配
- **注册服务中硬编码的角色ID**：学生=3，家长=4，教师=5
- **数据库中实际的角色ID**：学生=100，家长=101，教师=102
- 导致注册时分配了不存在的角色

### 2. 菜单权限不足
- 学生角色(ID=100)原本只有1个菜单权限
- 导致登录后只能看到很少的功能模块

### 3. 用户角色分配错误
- 部分用户被分配了错误的角色ID
- 用户ID 102 被分配了不存在的角色ID 3

## ✅ 修复方案

### 1. 修复注册服务中的角色ID
**文件**: `lingdu-framework/src/main/java/com/lingdu/framework/web/service/SysRegisterService.java`

**修改内容**:
```java
// 修改前
assignRole(userId, 3L);  // 学生
assignRole(userId, 4L);  // 家长  
assignRole(userId, 5L);  // 教师

// 修改后
assignRole(userId, 100L); // 学生
assignRole(userId, 101L); // 家长
assignRole(userId, 102L); // 教师
```

### 2. 修复数据库中的用户角色分配
```sql
-- 删除错误的角色分配
DELETE FROM sys_user_role WHERE user_id = 102 AND role_id = 3;

-- 为现有用户分配正确的学生角色
INSERT IGNORE INTO sys_user_role VALUES(100, 100); -- 李木子
INSERT IGNORE INTO sys_user_role VALUES(102, 100); -- cs
```

### 3. 增加学生角色的菜单权限
```sql
-- 清除现有权限
DELETE FROM sys_role_menu WHERE role_id IN (100, 101, 102);

-- 为学生角色分配基础菜单权限
INSERT INTO sys_role_menu VALUES
(100, 1),    -- 系统管理
(100, 100),  -- 用户管理
(100, 103),  -- 部门管理
(100, 104),  -- 岗位管理
(100, 2),    -- 系统监控
(100, 500),  -- 操作日志
(100, 501),  -- 登录日志
(100, 1001), -- 用户查询
(100, 1040), -- 操作查询
(100, 1042); -- 登录查询
```

## 🧪 验证结果

### 修复前
- 学生角色菜单权限：1个
- 用户角色分配：错误

### 修复后
- 学生角色菜单权限：10个
- 用户角色分配：正确

```sql
-- 验证用户角色分配
+---------+-----------+-----------+---------+--------------+
| user_id | user_name | nick_name | role_id | role_name    |
+---------+-----------+-----------+---------+--------------+
|     100 | 李木子    | 李木子    |     100 | 学生         |
|     101 | lmz       | 李木子    |       2 | 普通角色     |
|     102 | cs        | 黄裔雄    |     100 | 学生         |
+---------+-----------+-----------+---------+--------------+

-- 验证学生角色权限
+---------+-----------+------------+
| role_id | role_name | menu_count |
+---------+-----------+------------+
|     100 | 学生      |         10 |
+---------+-----------+------------+
```

## 🚀 测试建议

1. **重新登录测试**
   - 使用学生账号（李木子 或 cs）重新登录
   - 检查是否能看到更多功能模块

2. **新注册测试**
   - 注册新的学生账号
   - 验证是否正确分配学生角色(ID=100)

3. **权限验证**
   - 检查学生用户能否访问：
     - 系统管理模块
     - 用户管理功能
     - 系统监控功能
     - 个人信息管理

## 📝 注意事项

1. **服务重启**：修改后需要重启后端服务以清除缓存
2. **权限范围**：当前为学生分配的是基础权限，可根据需要调整
3. **数据一致性**：确保角色ID在代码和数据库中保持一致

## 🔧 相关文件

- **注册服务**：`lingdu-framework/src/main/java/com/lingdu/framework/web/service/SysRegisterService.java`
- **修复脚本**：`fix-student-permissions.sql`
- **验证脚本**：`check-and-fix-permissions.sql`

现在学生用户登录后应该能看到更多的功能模块了！
