<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="lingdu-common" />
        <module name="lingdu-system" />
        <module name="lingdu-admin" />
        <module name="lingdu-framework" />
        <module name="lingdu-quartz" />
        <module name="lingdu-generator" />
      </profile>
    </annotationProcessing>
  </component>
</project>