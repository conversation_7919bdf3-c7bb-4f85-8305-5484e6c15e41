<template>
    <div class="agent-page">
        <el-card class="header-card" shadow="never">
            <div class="header-content">
                <h1 class="page-title">
                    <i :class="currentSubject.icon || 'el-icon-cpu'"></i>
                    {{ currentSubject.name || 'AI智能助手' }}
                </h1>
                <p class="page-subtitle">{{ currentSubject.description || '与AI助手对话，获得学习指导和问题解答' }}</p>
            </div>
        </el-card>

        <div class="content">
            <el-row :gutter="24">
                <!-- 聊天区域 -->
                <el-col :span="24">
                    <el-card class="chat-card" shadow="hover">
                        <div class="chat-container">
                            <!-- 消息列表 -->
                            <div class="message-list" ref="messageList">
                                <div v-for="message in messages" :key="message.id" class="message-item"
                                    :class="{ 'user-message': message.isUser, 'ai-message': !message.isUser }">
                                    <div class="message-avatar">
                                        <i :class="message.isUser ? 'el-icon-user' : 'el-icon-cpu'"></i>
                                    </div>
                                    <div class="message-content">
                                        <div class="message-bubble">
                                            <div class="message-text" v-html="formatMessage(message.content)"></div>
                                            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 加载中状态 -->
                                <div v-if="isLoading" class="message-item ai-message">
                                    <div class="message-avatar">
                                        <i class="el-icon-cpu"></i>
                                    </div>
                                    <div class="message-content">
                                        <div class="message-bubble">
                                            <div class="typing-indicator">
                                                <span></span>
                                                <span></span>
                                                <span></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 输入区域 -->
                            <div class="input-area">
                                <el-form @submit.native.prevent="sendMessage">
                                    <el-row :gutter="12">
                                        <el-col :span="20">
                                            <el-input v-model="inputMessage" type="textarea" :rows="3"
                                                placeholder="请输入您的问题..." @keydown.ctrl.enter="sendMessage"
                                                :disabled="isLoading"></el-input>
                                        </el-col>
                                        <el-col :span="4">
                                            <el-button type="primary" @click="sendMessage" :loading="isLoading"
                                                :disabled="!inputMessage.trim()" class="send-btn">
                                                发送
                                            </el-button>
                                        </el-col>
                                    </el-row>
                                    <div class="input-tip">
                                        <span>按 Ctrl+Enter 快速发送</span>
                                    </div>
                                </el-form>
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <site-footer />
    </div>
</template>

<script>
import SiteFooter from '@/components/SiteFooter'

export default {
    name: 'AgentPage',
    components: { SiteFooter },
    data() {
        return {
            inputMessage: '',
            isLoading: false,
            currentSubject: {},
            messages: [
                {
                    id: 1,
                    content: '您好！我是灵渡AI学习助手，很高兴为您服务。我可以帮助您解答学习问题、提供学习建议、解释知识点等。请问有什么可以帮助您的吗？',
                    isUser: false,
                    timestamp: new Date()
                }
            ],
            subjects: {
                '小学数学': {
                    name: '小学数学智能助手',
                    icon: 'el-icon-reading',
                    description: '专为小学生设计的数学学习助手，帮助掌握基础数学概念和运算技能'
                },
                '初中数学': {
                    name: '初中数学智能助手',
                    icon: 'el-icon-reading',
                    description: '初中数学学习助手，涵盖代数、几何、函数等核心知识点'
                },
                '高中数学': {
                    name: '高中数学智能助手',
                    icon: 'el-icon-reading',
                    description: '高中数学学习助手，深度解析函数、几何、概率等高级数学概念'
                },
                '小学英语': {
                    name: '小学英语智能助手',
                    icon: 'el-icon-s-management',
                    description: '小学英语学习助手，培养英语兴趣和基础语言能力'
                },
                '初中英语': {
                    name: '初中英语智能助手',
                    icon: 'el-icon-s-management',
                    description: '初中英语学习助手，提升英语听说读写综合能力'
                },
                '高中英语': {
                    name: '高中英语智能助手',
                    icon: 'el-icon-s-management',
                    description: '高中英语学习助手，掌握高级英语语法和词汇应用'
                }
            }
        }
    },
    mounted() {
        this.setCurrentSubject()
    },
    methods: {
        setCurrentSubject() {
            // 从URL参数或路由状态获取当前学科
            const subjectName = this.$route.query.subject || this.$route.params.subject
            if (subjectName && this.subjects[subjectName]) {
                this.currentSubject = this.subjects[subjectName]
            } else {
                // 默认显示通用AI助手
                this.currentSubject = {
                    name: 'AI智能助手',
                    icon: 'el-icon-cpu',
                    description: '与AI助手对话，获得学习指导和问题解答'
                }
            }
        },
        async sendMessage() {
            if (!this.inputMessage.trim() || this.isLoading) return

            const userMessage = {
                id: Date.now(),
                content: this.inputMessage.trim(),
                isUser: true,
                timestamp: new Date()
            }

            this.messages.push(userMessage)
            const question = this.inputMessage.trim()
            this.inputMessage = ''
            this.isLoading = true

            // 滚动到底部
            this.scrollToBottom()

            try {
                // 模拟AI回复
                const response = await this.getAIResponse(question)

                const aiMessage = {
                    id: Date.now() + 1,
                    content: response,
                    isUser: false,
                    timestamp: new Date()
                }

                this.messages.push(aiMessage)
            } catch (error) {
                this.$message.error('发送失败，请重试')
            } finally {
                this.isLoading = false
                this.scrollToBottom()
            }
        },

        async getAIResponse(question) {
            // 模拟AI回复延迟
            await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))

            // 根据当前学科提供针对性回复
            const subjectName = this.currentSubject.name || ''
            let responses = {}

            if (subjectName.includes('小学数学')) {
                responses = {
                    '加法': '加法是数学的基础运算！我们可以用数数、画图、摆小棒等方法学习加法。比如 3+2=5，可以想象成3个苹果加上2个苹果等于5个苹果。',
                    '减法': '减法是加法的逆运算。比如 5-2=3，可以想象成5个苹果吃掉2个还剩3个。建议多做口算练习！',
                    '乘法': '乘法是加法的简便运算。比如 3×4=12，就是3个4相加。我们可以用九九乘法表来记忆。',
                    '除法': '除法是乘法的逆运算。比如 12÷3=4，就是12里面有几个3。',
                    '分数': '分数表示部分与整体的关系。比如1/2表示一半，1/4表示四分之一。',
                    '几何': '几何是研究图形的学科。我们学习点、线、面、体，还有各种图形的特征。',
                    '应用题': '应用题是数学在生活中的应用。我们要仔细读题，找出已知条件和问题，然后选择合适的计算方法。'
                }
            } else if (subjectName.includes('初中数学')) {
                responses = {
                    '代数': '代数是初中数学的重点！我们学习用字母表示数，建立方程和不等式。',
                    '几何': '初中几何包括三角形、四边形、圆等。要掌握各种图形的性质和判定定理。',
                    '函数': '函数是描述两个变量关系的工具。一次函数、二次函数都很重要。',
                    '方程': '方程是解决实际问题的重要工具。一元一次方程、二元一次方程组都要掌握。',
                    '不等式': '不等式帮助我们比较大小关系，解决实际问题。',
                    '统计': '统计帮助我们分析数据，做出判断。要会计算平均数、中位数、众数等。'
                }
            } else if (subjectName.includes('高中数学')) {
                responses = {
                    '函数': '高中函数更复杂，包括指数函数、对数函数、三角函数等。要掌握函数的性质和图像。',
                    '导数': '导数是微积分的基础，用来研究函数的变化率。',
                    '立体几何': '立体几何研究空间中的图形，需要空间想象能力。',
                    '解析几何': '解析几何用代数方法研究几何问题，建立坐标系。',
                    '概率': '概率研究随机事件发生的可能性。',
                    '数列': '数列是按一定规律排列的数，等差数列、等比数列是重点。'
                }
            } else if (subjectName.includes('小学英语')) {
                responses = {
                    '单词': '单词是英语的基础！建议用图片、动作、歌曲等方式记忆单词。',
                    '发音': '正确的发音很重要，可以多听英语儿歌和故事。',
                    '语法': '小学英语语法比较简单，主要是be动词、一般现在时等。',
                    '对话': '多练习日常对话，比如问候、介绍自己等。',
                    '阅读': '从简单的英语绘本开始，培养阅读兴趣。',
                    '听力': '多听英语音频，培养语感。'
                }
            } else if (subjectName.includes('初中英语')) {
                responses = {
                    '语法': '初中英语语法更系统，包括各种时态、语态、从句等。',
                    '词汇': '词汇量要扩大，建议每天背诵一定数量的单词。',
                    '阅读': '阅读理解是重点，要掌握阅读技巧。',
                    '写作': '写作要掌握基本句型，注意语法和拼写。',
                    '听力': '听力训练很重要，可以听英语新闻、歌曲等。',
                    '口语': '多练习口语，可以找同学对话或参加英语角。'
                }
            } else if (subjectName.includes('高中英语')) {
                responses = {
                    '语法': '高中英语语法更复杂，包括各种从句、虚拟语气等。',
                    '词汇': '词汇量要达到3500-4000个，包括高级词汇。',
                    '阅读': '阅读理解难度增加，要掌握各种阅读技巧。',
                    '写作': '写作要求更高，要掌握议论文、说明文等文体。',
                    '听力': '听力材料更复杂，包括新闻、讲座等。',
                    '翻译': '翻译是高中英语的新要求，要掌握中英文转换技巧。'
                }
            }

            // 通用回复
            const generalResponses = {
                '作业': '作业是巩固学习成果的重要方式。建议按时完成作业，遇到不会的题目可以及时请教老师或同学。',
                '考试': '考试是检验学习效果的方式。建议平时认真学习，考前做好复习准备。有什么考试技巧需要了解吗？',
                '学习': '学习是一个持续的过程，需要耐心和坚持。建议制定学习计划，找到适合自己的学习方法。',
                '帮助': '我很乐意帮助您！您可以问我关于学习的问题，我会尽力为您解答。'
            }

            // 合并学科特定回复和通用回复
            const allResponses = { ...responses, ...generalResponses }

            // 查找匹配的关键词
            for (const [keyword, response] of Object.entries(allResponses)) {
                if (question.includes(keyword)) {
                    return response
                }
            }

            // 根据学科提供默认回复
            if (subjectName.includes('数学')) {
                return '我是数学学习助手，可以帮您解答数学问题。您可以问我关于计算、几何、代数、函数等方面的问题。'
            } else if (subjectName.includes('英语')) {
                return '我是英语学习助手，可以帮您解答英语问题。您可以问我关于单词、语法、阅读、写作等方面的问题。'
            } else {
                return '感谢您的提问！虽然我还在学习中，但我会尽力帮助您。您可以问我关于学习的问题，我会尽力为您解答。'
            }
        },

        formatMessage(content) {
            // 简单的文本格式化
            return content.replace(/\n/g, '<br>')
        },

        formatTime(time) {
            return time.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            })
        },

        scrollToBottom() {
            this.$nextTick(() => {
                const messageList = this.$refs.messageList
                if (messageList) {
                    messageList.scrollTop = messageList.scrollHeight
                }
            })
        }
    }
}
</script>

<style scoped>
.agent-page {
    padding: 20px;
    min-height: 100vh;
    background: #f5f7fa;
}

.header-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-bottom: 20px;
    border: none;
}

.header-content {
    text-align: center;
    padding: 20px;
}

.page-title {
    margin: 0 0 8px;
    font-size: 28px;
    font-weight: 700;
}

.page-title i {
    margin-right: 8px;
}

.page-subtitle {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

.content {
    max-width: 1200px;
    margin: 0 auto;
}

.chat-card {
    height: 600px;
}

.chat-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.message-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: #fafafa;
    max-height: 480px;
    scroll-behavior: smooth;
}

.message-item {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-start;
}

.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 8px;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: #409EFF;
    color: white;
}

.ai-message .message-avatar {
    background: #67C23A;
    color: white;
}

.message-content {
    max-width: 70%;
    display: flex;
    flex-direction: column;
}

.user-message .message-content {
    align-items: flex-end;
}

.ai-message .message-content {
    align-items: flex-start;
}

.message-bubble {
    background: white;
    border-radius: 12px;
    padding: 12px 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
}

.user-message .message-bubble {
    background: #409EFF;
    color: white;
}

.message-text {
    line-height: 1.5;
    word-wrap: break-word;
}

.message-time {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
    text-align: right;
}

.user-message .message-time {
    color: rgba(255, 255, 255, 0.7);
}

.typing-indicator {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #409EFF;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {

    0%,
    60%,
    100% {
        transform: translateY(0);
        opacity: 0.5;
    }

    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

.input-area {
    padding: 16px;
    background: white;
    border-top: 1px solid #e4e7ed;
}

.send-btn {
    height: 100%;
    width: 100%;
}

.input-tip {
    text-align: right;
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
}
</style>
