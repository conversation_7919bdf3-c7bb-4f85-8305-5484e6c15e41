package com.lingdu.framework.web.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.lingdu.common.constant.CacheConstants;
import com.lingdu.common.constant.Constants;
import com.lingdu.common.constant.UserConstants;
import com.lingdu.common.core.domain.entity.SysUser;
import com.lingdu.system.domain.SysUserRole;
import com.lingdu.common.core.domain.model.RegisterBody;
import com.lingdu.common.core.redis.RedisCache;
import com.lingdu.common.exception.user.CaptchaException;
import com.lingdu.common.exception.user.CaptchaExpireException;
import com.lingdu.common.utils.DateUtils;
import com.lingdu.common.utils.MessageUtils;
import com.lingdu.common.utils.SecurityUtils;
import com.lingdu.common.utils.StringUtils;
import com.lingdu.framework.manager.AsyncManager;
import com.lingdu.framework.manager.factory.AsyncFactory;
import com.lingdu.system.domain.studentInfo;
import com.lingdu.system.domain.parentInfo;
import com.lingdu.system.domain.TeacherInfo;
import com.lingdu.system.service.ISysConfigService;
import com.lingdu.system.service.ISysUserService;
import com.lingdu.system.service.IstudentInfoService;
import com.lingdu.system.service.IparentInfoService;
import com.lingdu.system.service.ITeacherInfoService;
import com.lingdu.system.mapper.SysUserRoleMapper;
import java.util.ArrayList;
import java.util.List;

/**
 * 注册校验方法
 * 
 * <AUTHOR>
 */
@Component
public class SysRegisterService
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private IstudentInfoService studentInfoService;
    
    @Autowired
    private IparentInfoService parentInfoService;
    
    @Autowired
    private ITeacherInfoService teacherInfoService;
    
    @Autowired
    private SysUserRoleMapper userRoleMapper;

    /**
     * 注册
     */
    @Transactional
    public String register(RegisterBody registerBody)
    {
        String msg = "", username = registerBody.getUsername(), password = registerBody.getPassword();
        String userType = registerBody.getUserType();
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);

        // 验证码开关
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled)
        {
            validateCaptcha(username, registerBody.getCode(), registerBody.getUuid());
        }

        if (StringUtils.isEmpty(username))
        {
            msg = "用户名不能为空";
        }
        else if (StringUtils.isEmpty(password))
        {
            msg = "用户密码不能为空";
        }
        else if (StringUtils.isEmpty(userType))
        {
            msg = "用户类型不能为空";
        }
        else if (!"student".equals(userType) && !"parent".equals(userType) && !"teacher".equals(userType))
        {
            msg = "用户类型只能是student、parent或teacher";
        }
        else if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            msg = "账户长度必须在2到20个字符之间";
        }
        else if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            msg = "密码长度必须在5到20个字符之间";
        }
        else if (!userService.checkUserNameUnique(sysUser))
        {
            msg = "保存用户'" + username + "'失败，注册账号已存在";
        }
        else
        {
            try
            {
                // 设置用户基本信息
                sysUser.setNickName(StringUtils.isNotEmpty(registerBody.getRealName()) ? registerBody.getRealName() : username);
                sysUser.setPwdUpdateDate(DateUtils.getNowDate());
                sysUser.setPassword(SecurityUtils.encryptPassword(password));
                sysUser.setPhonenumber(registerBody.getPhone());
                
                // 注册用户
                boolean regFlag = userService.registerUser(sysUser);
                if (!regFlag)
                {
                    msg = "注册失败,请联系系统管理人员";
                }
                else
                {
                    // 获取新创建的用户ID
                    SysUser newUser = userService.selectUserByUserName(username);
                    Long userId = newUser.getUserId();
                    
                    // 根据用户类型分配角色和创建对应信息
                    if ("student".equals(userType))
                    {
                        // 分配学生角色（假设角色ID为3）
                        assignRole(userId, 3L);
                        // 创建学生信息
                        createStudentInfo(userId, registerBody);
                    }
                    else if ("parent".equals(userType))
                    {
                        // 分配家长角色（假设角色ID为4）
                        assignRole(userId, 4L);
                        // 创建家长信息
                        createParentInfo(userId, registerBody);
                    }
                    else if ("teacher".equals(userType))
                    {
                        // 分配教师角色（假设角色ID为5）
                        assignRole(userId, 5L);
                        // 创建教师信息
                        createTeacherInfo(userId, registerBody);
                    }
                    
                    AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.REGISTER, MessageUtils.message("user.register.success")));
                }
            }
            catch (Exception e)
            {
                msg = "注册失败：" + e.getMessage();
            }
        }
        return msg;
    }
    
    /**
     * 分配角色
     */
    private void assignRole(Long userId, Long roleId)
    {
        List<SysUserRole> list = new ArrayList<>();
        SysUserRole userRole = new SysUserRole();
        userRole.setUserId(userId);
        userRole.setRoleId(roleId);
        list.add(userRole);
        userRoleMapper.batchUserRole(list);
    }
    
    /**
     * 创建学生信息
     */
    private void createStudentInfo(Long userId, RegisterBody registerBody)
    {
        studentInfo student = new studentInfo();
        student.setUserId(userId);
        student.setNickName(registerBody.getRealName());
        student.setGrade(registerBody.getGrade());
        student.setStage(registerBody.getStage());
        student.setMainSubject(registerBody.getMainSubject());
        student.setExtraSubjects(registerBody.getExtraSubjects());
        student.setMembershipType("free"); // 默认免费会员
        studentInfoService.insertstudentInfo(student);
    }
    
    /**
     * 创建家长信息
     */
    private void createParentInfo(Long userId, RegisterBody registerBody)
    {
        parentInfo parent = new parentInfo();
        parent.setUserId(userId);
        parent.setUserName(registerBody.getUsername());
        parent.setNickName(registerBody.getRealName());
        parent.setPhone(registerBody.getPhone());
        parentInfoService.insertparentInfo(parent);
    }
    
    /**
     * 创建教师信息
     */
    private void createTeacherInfo(Long userId, RegisterBody registerBody)
    {
        TeacherInfo teacher = new TeacherInfo();
        teacher.setUserId(userId);
        teacher.setPhone(registerBody.getPhone());
        teacher.setName(registerBody.getRealName());
        teacher.setCertificateNo(registerBody.getCertificateNo());
        teacher.setVerifyStatus(0); // 待审核
        teacher.setStages(registerBody.getStages());
        teacher.setSubjects(registerBody.getSubjects());
        teacherInfoService.insertTeacherInfo(teacher);
    }

    /**
     * 校验验证码
     * 
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null)
        {
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha))
        {
            throw new CaptchaException();
        }
    }
}
