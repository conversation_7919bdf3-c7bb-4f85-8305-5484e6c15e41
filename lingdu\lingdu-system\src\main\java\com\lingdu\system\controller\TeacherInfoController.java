package com.lingdu.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.lingdu.common.annotation.Log;
import com.lingdu.common.core.controller.BaseController;
import com.lingdu.common.core.domain.AjaxResult;
import com.lingdu.common.enums.BusinessType;
import com.lingdu.system.domain.TeacherInfo;
import com.lingdu.system.service.ITeacherInfoService;
import com.lingdu.common.utils.poi.ExcelUtil;
import com.lingdu.common.core.page.TableDataInfo;

/**
 * 教师信息Controller
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
@RestController
@RequestMapping("/user/TeacherInfo")
public class TeacherInfoController extends BaseController
{
    @Autowired
    private ITeacherInfoService teacherInfoService;

    /**
     * 查询教师信息列表
     */
    @PreAuthorize("@ss.hasPermi('user:TeacherInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(TeacherInfo teacherInfo)
    {
        startPage();
        List<TeacherInfo> list = teacherInfoService.selectTeacherInfoList(teacherInfo);
        return getDataTable(list);
    }

    /**
     * 导出教师信息列表
     */
    @PreAuthorize("@ss.hasPermi('user:TeacherInfo:export')")
    @Log(title = "教师信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TeacherInfo teacherInfo)
    {
        List<TeacherInfo> list = teacherInfoService.selectTeacherInfoList(teacherInfo);
        ExcelUtil<TeacherInfo> util = new ExcelUtil<TeacherInfo>(TeacherInfo.class);
        util.exportExcel(response, list, "教师信息数据");
    }

    /**
     * 获取教师信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('user:TeacherInfo:query')")
    @GetMapping(value = "/{teacherId}")
    public AjaxResult getInfo(@PathVariable("teacherId") Long teacherId)
    {
        return success(teacherInfoService.selectTeacherInfoByTeacherId(teacherId));
    }

    /**
     * 新增教师信息
     */
    @PreAuthorize("@ss.hasPermi('user:TeacherInfo:add')")
    @Log(title = "教师信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TeacherInfo teacherInfo)
    {
        return toAjax(teacherInfoService.insertTeacherInfo(teacherInfo));
    }

    /**
     * 修改教师信息
     */
    @PreAuthorize("@ss.hasPermi('user:TeacherInfo:edit')")
    @Log(title = "教师信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TeacherInfo teacherInfo)
    {
        return toAjax(teacherInfoService.updateTeacherInfo(teacherInfo));
    }

    /**
     * 删除教师信息
     */
    @PreAuthorize("@ss.hasPermi('user:TeacherInfo:remove')")
    @Log(title = "教师信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{teacherIds}")
    public AjaxResult remove(@PathVariable Long[] teacherIds)
    {
        return toAjax(teacherInfoService.deleteTeacherInfoByTeacherIds(teacherIds));
    }
}
