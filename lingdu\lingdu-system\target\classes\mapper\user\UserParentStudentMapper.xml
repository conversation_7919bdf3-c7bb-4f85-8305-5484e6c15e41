<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lingdu.system.mapper.UserParentStudentMapper">
    
    <resultMap type="UserParentStudent" id="UserParentStudentResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="relation"    column="relation"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectUserParentStudentVo">
        select id, parent_id, student_id, relation, create_time from user_parent_student
    </sql>

    <select id="selectUserParentStudentList" parameterType="UserParentStudent" resultMap="UserParentStudentResult">
        <include refid="selectUserParentStudentVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="relation != null  and relation != ''"> and relation = #{relation}</if>
        </where>
    </select>
    
    <select id="selectUserParentStudentById" parameterType="Long" resultMap="UserParentStudentResult">
        <include refid="selectUserParentStudentVo"/>
        where id = #{id}
    </select>

    <insert id="insertUserParentStudent" parameterType="UserParentStudent" useGeneratedKeys="true" keyProperty="id">
        insert into user_parent_student
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="relation != null and relation != ''">relation,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="relation != null and relation != ''">#{relation},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateUserParentStudent" parameterType="UserParentStudent">
        update user_parent_student
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="relation != null and relation != ''">relation = #{relation},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserParentStudentById" parameterType="Long">
        delete from user_parent_student where id = #{id}
    </delete>

    <delete id="deleteUserParentStudentByIds" parameterType="String">
        delete from user_parent_student where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>