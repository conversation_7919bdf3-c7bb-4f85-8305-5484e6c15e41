<template>
    <div class="help-page">
        <el-card class="hero" shadow="never">
            <h1 class="title">帮助中心</h1>
            <p class="subtitle">常见问题与使用指南正在完善中。</p>
        </el-card>
        <site-footer />
    </div>
</template>

<script>
import SiteFooter from '@/components/SiteFooter'
export default {
    name: 'HelpPage',
    components: { SiteFooter }
}
</script>

<style scoped>
.help-page {
    padding: 20px;
}

.hero {
    margin: 0 auto;
    max-width: 1100px;
    border: none;
    background: #f5f7fa;
}

.title {
    margin: 0;
    padding: 16px 8px 4px;
    font-size: 22px;
    font-weight: 700;
    text-align: center;
}

.subtitle {
    margin: 0 0 16px;
    color: #909399;
    text-align: center;
}
</style>
