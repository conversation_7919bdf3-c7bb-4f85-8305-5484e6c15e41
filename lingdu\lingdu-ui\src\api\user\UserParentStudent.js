import request from '@/utils/request'

// 查询家长与学生绑定关系列表
export function listUserParentStudent(query) {
  return request({
    url: '/user/UserParentStudent/list',
    method: 'get',
    params: query
  })
}

// 查询家长与学生绑定关系详细
export function getUserParentStudent(id) {
  return request({
    url: '/user/UserParentStudent/' + id,
    method: 'get'
  })
}

// 新增家长与学生绑定关系
export function addUserParentStudent(data) {
  return request({
    url: '/user/UserParentStudent',
    method: 'post',
    data: data
  })
}

// 修改家长与学生绑定关系
export function updateUserParentStudent(data) {
  return request({
    url: '/user/UserParentStudent',
    method: 'put',
    data: data
  })
}

// 删除家长与学生绑定关系
export function delUserParentStudent(id) {
  return request({
    url: '/user/UserParentStudent/' + id,
    method: 'delete'
  })
}
