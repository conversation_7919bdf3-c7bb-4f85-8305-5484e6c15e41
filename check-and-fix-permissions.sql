-- 检查和修复学生权限的完整脚本

-- 第一步：检查当前系统中的菜单结构
SELECT menu_id, menu_name, parent_id, menu_type, path, component, visible, status 
FROM sys_menu 
WHERE status = '0' AND visible = '0'
ORDER BY parent_id, order_num;

-- 第二步：检查当前角色情况
SELECT role_id, role_name, role_key, status FROM sys_role ORDER BY role_id;

-- 第三步：检查用户角色分配情况
SELECT u.user_id, u.user_name, u.nick_name, ur.role_id, r.role_name 
FROM sys_user u 
LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id 
LEFT JOIN sys_role r ON ur.role_id = r.role_id 
ORDER BY u.user_id;

-- 第四步：检查角色菜单权限分配
SELECT rm.role_id, r.role_name, COUNT(rm.menu_id) as menu_count
FROM sys_role_menu rm 
LEFT JOIN sys_role r ON rm.role_id = r.role_id 
GROUP BY rm.role_id, r.role_name 
ORDER BY rm.role_id;
