<template>
  <div class="identity-select">
    <div class="container">
      <div class="header">
        <h1 class="title">{{ title }}</h1>
        <p class="subtitle">请选择您的身份类型</p>
      </div>
      
      <div class="cards-container">
        <!-- 学生卡片 -->
        <div class="identity-card student-card" @click="selectIdentity('student')">
          <div class="card-icon">
            <svg-icon icon-class="education" class="icon" />
          </div>
          <div class="card-content">
            <h3 class="card-title">学生</h3>
            <p class="card-description">学习课程、提交作业、查看学习进度</p>
            <div class="card-features">
              <span class="feature-tag">在线学习</span>
              <span class="feature-tag">作业提交</span>
              <span class="feature-tag">成绩查询</span>
            </div>
          </div>
          <div class="card-footer">
            <el-button type="primary" class="action-btn">开始学习</el-button>
          </div>
        </div>

        <!-- 家长卡片 -->
        <div class="identity-card parent-card" @click="selectIdentity('parent')">
          <div class="card-icon">
            <svg-icon icon-class="peoples" class="icon" />
          </div>
          <div class="card-content">
            <h3 class="card-title">家长</h3>
            <p class="card-description">管理孩子学习、监控学习进度、接收通知</p>
            <div class="card-features">
              <span class="feature-tag">学习监控</span>
              <span class="feature-tag">进度跟踪</span>
              <span class="feature-tag">消息通知</span>
            </div>
          </div>
          <div class="card-footer">
            <el-button type="success" class="action-btn">管理孩子</el-button>
          </div>
        </div>

        <!-- 教师卡片 -->
        <div class="identity-card teacher-card" @click="selectIdentity('teacher')">
          <div class="card-icon">
            <svg-icon icon-class="user" class="icon" />
          </div>
          <div class="card-content">
            <h3 class="card-title">教师</h3>
            <p class="card-description">管理学生、布置作业、批改作业、查看成绩</p>
            <div class="card-features">
              <span class="feature-tag">学生管理</span>
              <span class="feature-tag">作业批改</span>
              <span class="feature-tag">成绩统计</span>
            </div>
          </div>
          <div class="card-footer">
            <el-button type="warning" class="action-btn">开始教学</el-button>
          </div>
        </div>
      </div>

      <div class="footer-actions">
        <p class="login-tip">已有账号？</p>
        <el-button type="text" class="login-link" @click="goToLogin">直接登录</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "IdentitySelect",
  data() {
    return {
      title: process.env.VUE_APP_TITLE
    }
  },
  methods: {
    selectIdentity(identity) {
      // 跳转到对应身份的注册页面
      this.$router.push({
        path: '/register',
        query: { type: identity }
      })
    },
    goToLogin() {
      this.$router.push('/login')
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.identity-select {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../assets/images/login-background.jpg') center/cover;
    opacity: 0.1;
    z-index: 0;
  }

  // 添加动态背景粒子效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundShift 10s ease-in-out infinite;
    z-index: 0;
  }

  .container {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    width: 100%;
  }

  .header {
    text-align: center;
    margin-bottom: 60px;
    
    .title {
      font-size: 3rem;
      font-weight: 700;
      color: #fff;
      margin: 0 0 20px 0;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      animation: fadeInDown 1s ease-out;
      background: linear-gradient(45deg, #fff, #f0f8ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .subtitle {
      font-size: 1.2rem;
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
      animation: fadeInUp 1s ease-out 0.3s both;
    }
  }

  .cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
    perspective: 1000px;
  }

  .identity-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    padding: 45px 35px;
    text-align: center;
    cursor: pointer;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 
      0 15px 35px rgba(0, 0, 0, 0.1),
      0 5px 15px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 1s ease-out;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }

    // 光泽效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.6s ease;
    }

    // 边框光效
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 24px;
      padding: 2px;
      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: exclude;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-20px) rotateX(5deg) rotateY(5deg) scale(1.05);
      box-shadow: 
        0 30px 60px rgba(0, 0, 0, 0.25),
        0 15px 30px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);

      &::before {
        left: 100%;
      }

      &::after {
        opacity: 1;
      }

      .card-icon {
        transform: translateY(-10px);
        
        .icon {
          transform: scale(1.2) rotate(10deg);
        }
      }

      .card-content {
        .card-title {
          transform: translateY(-5px);
        }

        .feature-tag {
          transform: translateY(-3px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
      }

      .action-btn {
        transform: translateY(-5px) scale(1.05);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
      }
    }

    .card-icon {
      margin-bottom: 30px;
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      
      .icon {
        font-size: 4.5rem;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
      }
    }

    .card-content {
      margin-bottom: 35px;

      .card-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0 0 15px 0;
        color: #333;
        transition: all 0.3s ease;
        background: linear-gradient(45deg, #333, #666);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .card-description {
        font-size: 1.1rem;
        color: #666;
        line-height: 1.7;
        margin: 0 0 25px 0;
        font-weight: 400;
      }

      .card-features {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 12px;

        .feature-tag {
          background: #f8f9fa;
          color: #495057;
          padding: 8px 16px;
          border-radius: 25px;
          font-size: 0.9rem;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
          border: 1px solid rgba(0, 0, 0, 0.05);
          backdrop-filter: blur(5px);
        }
      }
    }

    .card-footer {
      .action-btn {
        padding: 15px 35px;
        border-radius: 30px;
        font-weight: 700;
        font-size: 1.1rem;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: none;
        min-width: 160px;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }

        &:hover::before {
          left: 100%;
        }
      }
    }

    // 不同身份卡片的特殊样式
    &.student-card {
      .card-icon .icon {
        color: #409EFF;
        background: linear-gradient(135deg, #409EFF, #66b1ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .feature-tag {
        background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
        color: #1890ff;
        border-color: rgba(24, 144, 255, 0.2);
      }

      &:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(230, 247, 255, 0.95));
      }
    }

    &.parent-card {
      .card-icon .icon {
        color: #67C23A;
        background: linear-gradient(135deg, #67C23A, #85ce61);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .feature-tag {
        background: linear-gradient(135deg, #f6ffed, #f9fff6);
        color: #52c41a;
        border-color: rgba(82, 196, 26, 0.2);
      }

      &:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(246, 255, 237, 0.95));
      }
    }

    &.teacher-card {
      .card-icon .icon {
        color: #E6A23C;
        background: linear-gradient(135deg, #E6A23C, #f0c78a);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .feature-tag {
        background: linear-gradient(135deg, #fffbe6, #fffef0);
        color: #faad14;
        border-color: rgba(250, 173, 20, 0.2);
      }

      &:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(255, 251, 230, 0.95));
      }
    }
  }

  .footer-actions {
    text-align: center;
    animation: fadeInUp 1s ease-out 0.6s both;

    .login-tip {
      color: rgba(255, 255, 255, 0.8);
      margin: 0 10px 0 0;
      display: inline-block;
      font-size: 1.1rem;
    }

    .login-link {
      color: #fff;
      font-size: 1.2rem;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s ease;
      padding: 8px 16px;
      border-radius: 20px;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(5px);

      &:hover {
        color: #ffd700;
        text-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
      }
    }
  }
}

// 动画定义
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes backgroundShift {
  0%, 100% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(-20px) translateY(-10px);
  }
  50% {
    transform: translateX(20px) translateY(10px);
  }
  75% {
    transform: translateX(-10px) translateY(20px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .identity-select {
    .cards-container {
      gap: 30px;
    }
  }
}

@media (max-width: 768px) {
  .identity-select {
    padding: 15px;

    .header {
      margin-bottom: 40px;
      
      .title {
        font-size: 2.2rem;
      }
      
      .subtitle {
        font-size: 1.1rem;
      }
    }
    
    .cards-container {
      grid-template-columns: 1fr;
      gap: 25px;
    }
    
    .identity-card {
      padding: 35px 25px;
      border-radius: 20px;

      &:hover {
        transform: translateY(-15px) scale(1.02);
      }

      .card-icon .icon {
        font-size: 3.5rem;
      }

      .card-content {
        .card-title {
          font-size: 1.6rem;
        }

        .card-description {
          font-size: 1rem;
        }

        .card-features {
          gap: 8px;

          .feature-tag {
            font-size: 0.85rem;
            padding: 6px 12px;
          }
        }
      }

      .card-footer .action-btn {
        padding: 12px 28px;
        font-size: 1rem;
        min-width: 140px;
      }
    }

    .footer-actions {
      .login-tip {
        font-size: 1rem;
      }

      .login-link {
        font-size: 1.1rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .identity-select {
    padding: 10px;

    .header {
      margin-bottom: 30px;
      
      .title {
        font-size: 1.8rem;
      }
      
      .subtitle {
        font-size: 1rem;
      }
    }
    
    .cards-container {
      gap: 20px;
    }
    
    .identity-card {
      padding: 30px 20px;

      .card-icon .icon {
        font-size: 3rem;
      }

      .card-content {
        margin-bottom: 25px;

        .card-title {
          font-size: 1.4rem;
        }

        .card-description {
          font-size: 0.95rem;
        }

        .card-features {
          .feature-tag {
            font-size: 0.8rem;
            padding: 5px 10px;
          }
        }
      }

      .card-footer .action-btn {
        padding: 10px 24px;
        font-size: 0.95rem;
        min-width: 120px;
      }
    }

    .footer-actions {
      .login-tip, .login-link {
        font-size: 0.95rem;
      }
    }
  }
}
</style>

