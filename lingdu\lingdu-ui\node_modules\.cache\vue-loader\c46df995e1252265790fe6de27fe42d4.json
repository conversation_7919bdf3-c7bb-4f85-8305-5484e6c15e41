{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\register.vue", "mtime": 1758268597404}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758264043508}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758264043933}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldENvZGVJbWcsIHJlZ2lzdGVyIH0gZnJvbSAiQC9hcGkvbG9naW4iCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlJlZ2lzdGVyIiwKICBkYXRhKCkgewogICAgY29uc3QgZXF1YWxUb1Bhc3N3b3JkID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gewogICAgICBpZiAodGhpcy5yZWdpc3RlckZvcm0ucGFzc3dvcmQgIT09IHZhbHVlKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLkuKTmrKHovpPlhaXnmoTlr4bnoIHkuI3kuIDoh7QiKSkKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpCiAgICAgIH0KICAgIH0KICAgIHJldHVybiB7CiAgICAgIHRpdGxlOiBwcm9jZXNzLmVudi5WVUVfQVBQX1RJVExFLAogICAgICBjb2RlVXJsOiAiIiwKICAgICAgcmVnaXN0ZXJGb3JtOiB7CiAgICAgICAgdXNlclR5cGU6IHRoaXMuJHJvdXRlLnF1ZXJ5LnR5cGUgfHwgInN0dWRlbnQiLAogICAgICAgIHVzZXJuYW1lOiAiIiwKICAgICAgICBwYXNzd29yZDogIiIsCiAgICAgICAgY29uZmlybVBhc3N3b3JkOiAiIiwKICAgICAgICBwaG9uZTogIiIsCiAgICAgICAgcmVhbE5hbWU6ICIiLAogICAgICAgIGdyYWRlOiAiIiwKICAgICAgICBzdGFnZTogIiIsCiAgICAgICAgbWFpblN1YmplY3Q6ICIiLAogICAgICAgIGV4dHJhU3ViamVjdHM6ICIiLAogICAgICAgIGNlcnRpZmljYXRlTm86ICIiLAogICAgICAgIHN0YWdlczogIiIsCiAgICAgICAgc3ViamVjdHM6ICIiLAogICAgICAgIGNvZGU6ICIiLAogICAgICAgIHV1aWQ6ICIiCiAgICAgIH0sCiAgICAgIGlkZW50aXR5VHlwZXM6IFsKICAgICAgICB7IHZhbHVlOiAic3R1ZGVudCIsIGxhYmVsOiAi5a2m55SfIiwgaWNvbjogImVkdWNhdGlvbiIgfSwKICAgICAgICB7IHZhbHVlOiAicGFyZW50IiwgbGFiZWw6ICLlrrbplb8iLCBpY29uOiAicGVvcGxlcyIgfSwKICAgICAgICB7IHZhbHVlOiAidGVhY2hlciIsIGxhYmVsOiAi5pWZ5biIIiwgaWNvbjogInVzZXIiIH0KICAgICAgXSwKICAgICAgcmVnaXN0ZXJSdWxlczogewogICAgICAgIHVzZXJUeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiY2hhbmdlIiwgbWVzc2FnZTogIuivt+mAieaLqeeUqOaIt+exu+WeiyIgfQogICAgICAgIF0sCiAgICAgICAgdXNlcm5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJibHVyIiwgbWVzc2FnZTogIuivt+i+k+WFpeaCqOeahOi0puWPtyIgfSwKICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDIwLCBtZXNzYWdlOiAn55So5oi36LSm5Y+36ZW/5bqm5b+F6aG75LuL5LqOIDIg5ZKMIDIwIOS5i+mXtCcsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBwYXNzd29yZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImJsdXIiLCBtZXNzYWdlOiAi6K+36L6T5YWl5oKo55qE5a+G56CBIiB9LAogICAgICAgICAgeyBtaW46IDUsIG1heDogMjAsIG1lc3NhZ2U6ICLnlKjmiLflr4bnoIHplb/luqblv4Xpobvku4vkuo4gNSDlkowgMjAg5LmL6Ze0IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IHBhdHRlcm46IC9eW148PiInfFxcXSskLywgbWVzc2FnZTogIuS4jeiDveWMheWQq+mdnuazleWtl+espu+8mjwgPiBcIiAnIFxcXCB8IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGNvbmZpcm1QYXNzd29yZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImJsdXIiLCBtZXNzYWdlOiAi6K+35YaN5qyh6L6T5YWl5oKo55qE5a+G56CBIiB9LAogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiBlcXVhbFRvUGFzc3dvcmQsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBwaG9uZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImJsdXIiLCBtZXNzYWdlOiAi6K+36L6T5YWl5omL5py65Y+3IiB9LAogICAgICAgICAgeyBwYXR0ZXJuOiAvXjFbMy05XVxkezl9JC8sIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fmoLzlvI8iLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgcmVhbE5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJibHVyIiwgbWVzc2FnZTogIuivt+i+k+WFpeecn+WunuWnk+WQjSIgfQogICAgICAgIF0sCiAgICAgICAgZ3JhZGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJjaGFuZ2UiLCBtZXNzYWdlOiAi6K+36YCJ5oup5bm057qnIiB9CiAgICAgICAgXSwKICAgICAgICBzdGFnZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImNoYW5nZSIsIG1lc3NhZ2U6ICLor7fpgInmi6nlrabmrrUiIH0KICAgICAgICBdLAogICAgICAgIG1haW5TdWJqZWN0OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIG1lc3NhZ2U6ICLor7fovpPlhaXkuLvlrabnp5EiIH0KICAgICAgICBdLAogICAgICAgIGNlcnRpZmljYXRlTm86IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJibHVyIiwgbWVzc2FnZTogIuivt+i+k+WFpeaVmeW4iOi1hOagvOivgee8luWPtyIgfQogICAgICAgIF0sCiAgICAgICAgc3RhZ2VzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIG1lc3NhZ2U6ICLor7fovpPlhaXmlZnmjojlrabmrrUiIH0KICAgICAgICBdLAogICAgICAgIHN1YmplY3RzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIG1lc3NhZ2U6ICLor7fovpPlhaXmlZnmjojlrabnp5EiIH0KICAgICAgICBdLAogICAgICAgIGNvZGU6IFt7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiY2hhbmdlIiwgbWVzc2FnZTogIuivt+i+k+WFpemqjOivgeeggSIgfV0KICAgICAgfSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGNhcHRjaGFFbmFibGVkOiB0cnVlCiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRDb2RlKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldENvZGUoKSB7CiAgICAgIGdldENvZGVJbWcoKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5jYXB0Y2hhRW5hYmxlZCA9IHJlcy5jYXB0Y2hhRW5hYmxlZCA9PT0gdW5kZWZpbmVkID8gdHJ1ZSA6IHJlcy5jYXB0Y2hhRW5hYmxlZAogICAgICAgIGlmICh0aGlzLmNhcHRjaGFFbmFibGVkKSB7CiAgICAgICAgICB0aGlzLmNvZGVVcmwgPSAiZGF0YTppbWFnZS9naWY7YmFzZTY0LCIgKyByZXMuaW1nCiAgICAgICAgICB0aGlzLnJlZ2lzdGVyRm9ybS51dWlkID0gcmVzLnV1aWQKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgc3dpdGNoSWRlbnRpdHkodHlwZSkgewogICAgICB0aGlzLnJlZ2lzdGVyRm9ybS51c2VyVHlwZSA9IHR5cGUKICAgICAgLy8g5riF56m654m55a6a5a2X5q61CiAgICAgIHRoaXMucmVnaXN0ZXJGb3JtLmdyYWRlID0gIiIKICAgICAgdGhpcy5yZWdpc3RlckZvcm0uc3RhZ2UgPSAiIgogICAgICB0aGlzLnJlZ2lzdGVyRm9ybS5tYWluU3ViamVjdCA9ICIiCiAgICAgIHRoaXMucmVnaXN0ZXJGb3JtLmV4dHJhU3ViamVjdHMgPSAiIgogICAgICB0aGlzLnJlZ2lzdGVyRm9ybS5jZXJ0aWZpY2F0ZU5vID0gIiIKICAgICAgdGhpcy5yZWdpc3RlckZvcm0uc3RhZ2VzID0gIiIKICAgICAgdGhpcy5yZWdpc3RlckZvcm0uc3ViamVjdHMgPSAiIgogICAgICAKICAgICAgLy8g5riF6Zmk6aqM6K+BCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzLnJlZ2lzdGVyRm9ybS5jbGVhclZhbGlkYXRlKCkKICAgICAgfSkKICAgIH0sCiAgICBnb0JhY2soKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvaWRlbnRpdHktc2VsZWN0JykKICAgIH0sCiAgICBnZXRJZGVudGl0eUljb24oKSB7CiAgICAgIGNvbnN0IHR5cGUgPSB0aGlzLmlkZW50aXR5VHlwZXMuZmluZCh0ID0+IHQudmFsdWUgPT09IHRoaXMucmVnaXN0ZXJGb3JtLnVzZXJUeXBlKQogICAgICByZXR1cm4gdHlwZSA/IHR5cGUuaWNvbiA6ICd1c2VyJwogICAgfSwKICAgIGdldElkZW50aXR5VGl0bGUoKSB7CiAgICAgIGNvbnN0IHR5cGUgPSB0aGlzLmlkZW50aXR5VHlwZXMuZmluZCh0ID0+IHQudmFsdWUgPT09IHRoaXMucmVnaXN0ZXJGb3JtLnVzZXJUeXBlKQogICAgICByZXR1cm4gdHlwZSA/IGAke3R5cGUubGFiZWx95rOo5YaMYCA6ICfnlKjmiLfms6jlhownCiAgICB9LAogICAgZ2V0SWRlbnRpdHlTdWJ0aXRsZSgpIHsKICAgICAgY29uc3Qgc3VidGl0bGVzID0gewogICAgICAgIHN0dWRlbnQ6ICflvIDlkK/mgqjnmoTlrabkuaDkuYvml4UnLAogICAgICAgIHBhcmVudDogJ+WFs+azqOWtqeWtkOeahOWtpuS5oOaIkOmVvycsCiAgICAgICAgdGVhY2hlcjogJ+W8gOWQr+aCqOeahOaVmeWtpueuoeeQhicKICAgICAgfQogICAgICByZXR1cm4gc3VidGl0bGVzW3RoaXMucmVnaXN0ZXJGb3JtLnVzZXJUeXBlXSB8fCAn5qyi6L+O5Yqg5YWlJwogICAgfSwKICAgIGdldFJlZ2lzdGVyQnV0dG9uVGV4dCgpIHsKICAgICAgY29uc3QgdGV4dHMgPSB7CiAgICAgICAgc3R1ZGVudDogJ+W8gOWni+WtpuS5oCcsCiAgICAgICAgcGFyZW50OiAn5byA5aeL566h55CGJywKICAgICAgICB0ZWFjaGVyOiAn5byA5aeL5pWZ5a2mJwogICAgICB9CiAgICAgIHJldHVybiB0ZXh0c1t0aGlzLnJlZ2lzdGVyRm9ybS51c2VyVHlwZV0gfHwgJ+azqOWGjCcKICAgIH0sCiAgICBoYW5kbGVSZWdpc3RlcigpIHsKICAgICAgdGhpcy4kcmVmcy5yZWdpc3RlckZvcm0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICAgICAgcmVnaXN0ZXIodGhpcy5yZWdpc3RlckZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgY29uc3QgdXNlcm5hbWUgPSB0aGlzLnJlZ2lzdGVyRm9ybS51c2VybmFtZQogICAgICAgICAgICB0aGlzLiRhbGVydCgiPGZvbnQgY29sb3I9J3JlZCc+5oGt5Zac5L2g77yM5oKo55qE6LSm5Y+3ICIgKyB1c2VybmFtZSArICIg5rOo5YaM5oiQ5Yqf77yBPC9mb250PiIsICfns7vnu5/mj5DnpLonLCB7CiAgICAgICAgICAgICAgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiB0cnVlLAogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL2xvZ2luIikKICAgICAgICAgICAgfSkuY2F0Y2goKCkgPT4ge30pCiAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgICAgICAgIGlmICh0aGlzLmNhcHRjaGFFbmFibGVkKSB7CiAgICAgICAgICAgICAgdGhpcy5nZXRDb2RlKCkKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsQA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"register\">\n    <div class=\"register-container\">\n      <!-- 返回按钮 -->\n      <div class=\"back-btn\" @click=\"goBack\">\n        <svg-icon icon-class=\"arrow-left\" class=\"back-icon\" />\n        <span>返回选择</span>\n      </div>\n\n      <!-- 注册卡片 -->\n      <div class=\"register-card\" :class=\"`${registerForm.userType}-theme`\">\n        <div class=\"card-header\">\n          <div class=\"identity-icon\">\n            <svg-icon :icon-class=\"getIdentityIcon()\" class=\"icon\" />\n          </div>\n          <h2 class=\"card-title\">{{ getIdentityTitle() }}</h2>\n          <p class=\"card-subtitle\">{{ getIdentitySubtitle() }}</p>\n        </div>\n\n        <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\n          <!-- 身份切换 -->\n          <div class=\"identity-switch\">\n            <div \n              v-for=\"type in identityTypes\" \n              :key=\"type.value\"\n              class=\"switch-item\"\n              :class=\"{ active: registerForm.userType === type.value }\"\n              @click=\"switchIdentity(type.value)\"\n            >\n              <svg-icon :icon-class=\"type.icon\" class=\"switch-icon\" />\n              <span>{{ type.label }}</span>\n            </div>\n          </div>\n\n          <!-- 基本信息 -->\n          <div class=\"form-section\">\n            <h4 class=\"section-title\">基本信息</h4>\n            \n            <div class=\"form-row\">\n              <el-form-item prop=\"username\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"user\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.username\"\n                    type=\"text\"\n                    auto-complete=\"off\"\n                    placeholder=\"请输入账号\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"phone\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"phone\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.phone\"\n                    type=\"text\"\n                    auto-complete=\"off\"\n                    placeholder=\"请输入手机号\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n\n            <el-form-item prop=\"realName\">\n              <div class=\"input-group\">\n                <svg-icon icon-class=\"user\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"registerForm.realName\"\n                  type=\"text\"\n                  auto-complete=\"off\"\n                  placeholder=\"请输入真实姓名\"\n                  class=\"custom-input\"\n                />\n              </div>\n            </el-form-item>\n\n            <div class=\"form-row\">\n              <el-form-item prop=\"password\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"password\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.password\"\n                    type=\"password\"\n                    auto-complete=\"off\"\n                    placeholder=\"请输入密码\"\n                    class=\"custom-input\"\n                    @keyup.enter.native=\"handleRegister\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"confirmPassword\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"password\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.confirmPassword\"\n                    type=\"password\"\n                    auto-complete=\"off\"\n                    placeholder=\"确认密码\"\n                    class=\"custom-input\"\n                    @keyup.enter.native=\"handleRegister\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 学生专用信息 -->\n          <div v-if=\"registerForm.userType === 'student'\" class=\"form-section\">\n            <h4 class=\"section-title\">学习信息</h4>\n            \n            <div class=\"form-row\">\n              <el-form-item prop=\"grade\" class=\"form-item-half\">\n                <el-select v-model=\"registerForm.grade\" placeholder=\"请选择年级\" class=\"custom-select\">\n                  <el-option label=\"小学一年级\" value=\"小学一年级\"></el-option>\n                  <el-option label=\"小学二年级\" value=\"小学二年级\"></el-option>\n                  <el-option label=\"小学三年级\" value=\"小学三年级\"></el-option>\n                  <el-option label=\"小学四年级\" value=\"小学四年级\"></el-option>\n                  <el-option label=\"小学五年级\" value=\"小学五年级\"></el-option>\n                  <el-option label=\"小学六年级\" value=\"小学六年级\"></el-option>\n                  <el-option label=\"初中一年级\" value=\"初中一年级\"></el-option>\n                  <el-option label=\"初中二年级\" value=\"初中二年级\"></el-option>\n                  <el-option label=\"初中三年级\" value=\"初中三年级\"></el-option>\n                  <el-option label=\"高中一年级\" value=\"高中一年级\"></el-option>\n                  <el-option label=\"高中二年级\" value=\"高中二年级\"></el-option>\n                  <el-option label=\"高中三年级\" value=\"高中三年级\"></el-option>\n                </el-select>\n              </el-form-item>\n\n              <el-form-item prop=\"stage\" class=\"form-item-half\">\n                <el-select v-model=\"registerForm.stage\" placeholder=\"请选择学段\" class=\"custom-select\">\n                  <el-option label=\"小学\" value=\"小学\"></el-option>\n                  <el-option label=\"初中\" value=\"初中\"></el-option>\n                  <el-option label=\"高中\" value=\"高中\"></el-option>\n                </el-select>\n              </el-form-item>\n            </div>\n\n            <div class=\"form-row\">\n              <el-form-item prop=\"mainSubject\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.mainSubject\"\n                    type=\"text\"\n                    placeholder=\"主学科（如：数学）\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"extraSubjects\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.extraSubjects\"\n                    type=\"text\"\n                    placeholder=\"副学科\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 教师专用信息 -->\n          <div v-if=\"registerForm.userType === 'teacher'\" class=\"form-section\">\n            <h4 class=\"section-title\">教学信息</h4>\n            \n            <el-form-item prop=\"certificateNo\">\n              <div class=\"input-group\">\n                <svg-icon icon-class=\"documentation\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"registerForm.certificateNo\"\n                  type=\"text\"\n                  placeholder=\"请输入教师资格证编号\"\n                  class=\"custom-input\"\n                />\n              </div>\n            </el-form-item>\n\n            <div class=\"form-row\">\n              <el-form-item prop=\"stages\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.stages\"\n                    type=\"text\"\n                    placeholder=\"教授学段（如：小学、初中、高中）\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n\n              <el-form-item prop=\"subjects\" class=\"form-item-half\">\n                <div class=\"input-group\">\n                  <svg-icon icon-class=\"education\" class=\"input-icon\" />\n                  <el-input\n                    v-model=\"registerForm.subjects\"\n                    type=\"text\"\n                    placeholder=\"教授学科（如：数学、语文、英语）\"\n                    class=\"custom-input\"\n                  />\n                </div>\n              </el-form-item>\n            </div>\n          </div>\n\n          <!-- 验证码 -->\n          <el-form-item prop=\"code\" v-if=\"captchaEnabled\" class=\"captcha-section\">\n            <div class=\"captcha-group\">\n              <div class=\"input-group captcha-input\">\n                <svg-icon icon-class=\"validCode\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"registerForm.code\"\n                  auto-complete=\"off\"\n                  placeholder=\"请输入验证码\"\n                  class=\"custom-input\"\n                  @keyup.enter.native=\"handleRegister\"\n                />\n              </div>\n              <div class=\"captcha-image\" @click=\"getCode\">\n                <img :src=\"codeUrl\" class=\"captcha-img\"/>\n                <div class=\"refresh-tip\">点击刷新</div>\n              </div>\n            </div>\n          </el-form-item>\n\n          <!-- 注册按钮 -->\n          <el-form-item class=\"register-btn-item\">\n            <el-button\n              :loading=\"loading\"\n              type=\"primary\"\n              class=\"register-btn\"\n              @click.native.prevent=\"handleRegister\"\n            >\n              <span v-if=\"!loading\">{{ getRegisterButtonText() }}</span>\n              <span v-else>注册中...</span>\n            </el-button>\n          </el-form-item>\n\n          <div class=\"login-link\">\n            <span>已有账号？</span>\n            <router-link to=\"/login\" class=\"link\">立即登录</router-link>\n          </div>\n        </el-form>\n      </div>\n\n      <!-- 装饰性元素 -->\n      <div class=\"decoration\">\n        <div class=\"circle circle-1\"></div>\n        <div class=\"circle circle-2\"></div>\n        <div class=\"circle circle-3\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg, register } from \"@/api/login\"\n\nexport default {\n  name: \"Register\",\n  data() {\n    const equalToPassword = (rule, value, callback) => {\n      if (this.registerForm.password !== value) {\n        callback(new Error(\"两次输入的密码不一致\"))\n      } else {\n        callback()\n      }\n    }\n    return {\n      title: process.env.VUE_APP_TITLE,\n      codeUrl: \"\",\n      registerForm: {\n        userType: this.$route.query.type || \"student\",\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        phone: \"\",\n        realName: \"\",\n        grade: \"\",\n        stage: \"\",\n        mainSubject: \"\",\n        extraSubjects: \"\",\n        certificateNo: \"\",\n        stages: \"\",\n        subjects: \"\",\n        code: \"\",\n        uuid: \"\"\n      },\n      identityTypes: [\n        { value: \"student\", label: \"学生\", icon: \"education\" },\n        { value: \"parent\", label: \"家长\", icon: \"peoples\" },\n        { value: \"teacher\", label: \"教师\", icon: \"user\" }\n      ],\n      registerRules: {\n        userType: [\n          { required: true, trigger: \"change\", message: \"请选择用户类型\" }\n        ],\n        username: [\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\n          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\n          { min: 5, max: 20, message: \"用户密码长度必须介于 5 和 20 之间\", trigger: \"blur\" },\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\ |\", trigger: \"blur\" }\n        ],\n        confirmPassword: [\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\n        ],\n        phone: [\n          { required: true, trigger: \"blur\", message: \"请输入手机号\" },\n          { pattern: /^1[3-9]\\d{9}$/, message: \"请输入正确的手机号格式\", trigger: \"blur\" }\n        ],\n        realName: [\n          { required: true, trigger: \"blur\", message: \"请输入真实姓名\" }\n        ],\n        grade: [\n          { required: true, trigger: \"change\", message: \"请选择年级\" }\n        ],\n        stage: [\n          { required: true, trigger: \"change\", message: \"请选择学段\" }\n        ],\n        mainSubject: [\n          { required: true, trigger: \"blur\", message: \"请输入主学科\" }\n        ],\n        certificateNo: [\n          { required: true, trigger: \"blur\", message: \"请输入教师资格证编号\" }\n        ],\n        stages: [\n          { required: true, trigger: \"blur\", message: \"请输入教授学段\" }\n        ],\n        subjects: [\n          { required: true, trigger: \"blur\", message: \"请输入教授学科\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      captchaEnabled: true\n    }\n  },\n  created() {\n    this.getCode()\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img\n          this.registerForm.uuid = res.uuid\n        }\n      })\n    },\n    switchIdentity(type) {\n      this.registerForm.userType = type\n      // 清空特定字段\n      this.registerForm.grade = \"\"\n      this.registerForm.stage = \"\"\n      this.registerForm.mainSubject = \"\"\n      this.registerForm.extraSubjects = \"\"\n      this.registerForm.certificateNo = \"\"\n      this.registerForm.stages = \"\"\n      this.registerForm.subjects = \"\"\n      \n      // 清除验证\n      this.$nextTick(() => {\n        this.$refs.registerForm.clearValidate()\n      })\n    },\n    goBack() {\n      this.$router.push('/identity-select')\n    },\n    getIdentityIcon() {\n      const type = this.identityTypes.find(t => t.value === this.registerForm.userType)\n      return type ? type.icon : 'user'\n    },\n    getIdentityTitle() {\n      const type = this.identityTypes.find(t => t.value === this.registerForm.userType)\n      return type ? `${type.label}注册` : '用户注册'\n    },\n    getIdentitySubtitle() {\n      const subtitles = {\n        student: '开启您的学习之旅',\n        parent: '关注孩子的学习成长',\n        teacher: '开启您的教学管理'\n      }\n      return subtitles[this.registerForm.userType] || '欢迎加入'\n    },\n    getRegisterButtonText() {\n      const texts = {\n        student: '开始学习',\n        parent: '开始管理',\n        teacher: '开始教学'\n      }\n      return texts[this.registerForm.userType] || '注册'\n    },\n    handleRegister() {\n      this.$refs.registerForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n          register(this.registerForm).then(res => {\n            const username = this.registerForm.username\n            this.$alert(\"<font color='red'>恭喜你，您的账号 \" + username + \" 注册成功！</font>\", '系统提示', {\n              dangerouslyUseHTMLString: true,\n              type: 'success'\n            }).then(() => {\n              this.$router.push(\"/login\")\n            }).catch(() => {})\n          }).catch(() => {\n            this.loading = false\n            if (this.captchaEnabled) {\n              this.getCode()\n            }\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\n.register {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: url(\"../assets/images/login-background.jpg\") center/cover;\n    opacity: 0.1;\n    z-index: 0;\n  }\n\n  // 添加动态背景粒子效果\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-image: \n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);\n    animation: backgroundShift 8s ease-in-out infinite;\n    z-index: 0;\n  }\n\n  .register-container {\n    position: relative;\n    z-index: 1;\n    width: 100%;\n    max-width: 600px;\n  }\n\n  .back-btn {\n    position: absolute;\n    top: -60px;\n    left: 0;\n    display: flex;\n    align-items: center;\n    color: rgba(255, 255, 255, 0.9);\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n    font-weight: 500;\n\n    &:hover {\n      color: #fff;\n      transform: translateX(-5px);\n    }\n\n    .back-icon {\n      margin-right: 8px;\n      font-size: 16px;\n    }\n  }\n\n  .register-card {\n    background: rgba(255, 255, 255, 0.95);\n    border-radius: 24px;\n    padding: 45px;\n    box-shadow: \n      0 25px 50px rgba(0, 0, 0, 0.15),\n      0 10px 20px rgba(0, 0, 0, 0.1),\n      inset 0 1px 0 rgba(255, 255, 255, 0.6);\n    backdrop-filter: blur(15px);\n    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n    animation: slideInUp 0.8s ease-out;\n    position: relative;\n    overflow: hidden;\n    border: 1px solid rgba(255, 255, 255, 0.2);\n\n    // 光泽效果\n    &::before {\n      content: '';\n      position: absolute;\n      top: -50%;\n      left: -50%;\n      width: 200%;\n      height: 200%;\n      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);\n      transform: rotate(45deg);\n      transition: all 0.6s ease;\n      opacity: 0;\n      pointer-events: none;\n      z-index: 1;\n    }\n\n    // 边框光效\n    &::after {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      border-radius: 24px;\n      padding: 2px;\n      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n      mask-composite: exclude;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n      pointer-events: none;\n      z-index: 1;\n    }\n\n    &:hover {\n      transform: translateY(-8px) scale(1.02);\n      box-shadow: \n        0 35px 70px rgba(0, 0, 0, 0.25),\n        0 15px 30px rgba(0, 0, 0, 0.15),\n        inset 0 1px 0 rgba(255, 255, 255, 0.8);\n\n      &::before {\n        opacity: 1;\n        transform: rotate(45deg) translate(50%, 50%);\n      }\n\n      &::after {\n        opacity: 1;\n      }\n    }\n\n    .card-header {\n      text-align: center;\n      margin-bottom: 30px;\n\n      .identity-icon {\n        width: 80px;\n        height: 80px;\n        margin: 0 auto 20px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.3s ease;\n\n        .icon {\n          font-size: 2.5rem;\n        }\n      }\n\n      .card-title {\n        font-size: 1.8rem;\n        font-weight: 600;\n        margin: 0 0 10px 0;\n        color: #333;\n      }\n\n      .card-subtitle {\n        font-size: 1rem;\n        color: #666;\n        margin: 0;\n      }\n    }\n\n    .identity-switch {\n      display: flex;\n      background: rgba(245, 245, 245, 0.8);\n      border-radius: 16px;\n      padding: 6px;\n      margin-bottom: 35px;\n      backdrop-filter: blur(5px);\n      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);\n\n      .switch-item {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 14px 10px;\n        border-radius: 12px;\n        cursor: pointer;\n        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n        font-size: 15px;\n        font-weight: 600;\n        position: relative;\n        overflow: hidden;\n\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));\n          border-radius: 12px;\n          opacity: 0;\n          transition: opacity 0.3s ease;\n          pointer-events: none;\n        }\n\n        .switch-icon {\n          margin-right: 8px;\n          font-size: 18px;\n          transition: all 0.3s ease;\n        }\n\n        &.active {\n          background: #fff;\n          color: #409EFF;\n          box-shadow: \n            0 4px 12px rgba(0, 0, 0, 0.15),\n            0 2px 4px rgba(64, 158, 255, 0.2);\n          transform: translateY(-2px);\n\n          &::before {\n            opacity: 1;\n          }\n\n          .switch-icon {\n            transform: scale(1.1);\n          }\n        }\n\n        &:hover:not(.active) {\n          background: rgba(255, 255, 255, 0.9);\n          transform: translateY(-1px);\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n        }\n      }\n    }\n\n    .register-form {\n      position: relative;\n      z-index: 2;\n      \n      .form-section {\n        margin-bottom: 30px;\n\n        .section-title {\n          font-size: 1.1rem;\n          font-weight: 600;\n          color: #333;\n          margin: 0 0 20px 0;\n          padding-bottom: 10px;\n          border-bottom: 2px solid #f0f0f0;\n        }\n\n        .form-row {\n          display: flex;\n          gap: 15px;\n          margin-bottom: 20px;\n\n          .form-item-half {\n            flex: 1;\n            margin-bottom: 0;\n          }\n        }\n\n        .input-group {\n          position: relative;\n          margin-bottom: 25px;\n\n          .input-icon {\n            position: absolute;\n            left: 20px;\n            top: 50%;\n            transform: translateY(-50%);\n            color: #999;\n            font-size: 16px;\n            z-index: 3;\n            transition: all 0.3s ease;\n          }\n\n          .custom-input {\n            ::v-deep .el-input__inner {\n              height: 55px !important;\n              padding-left: 60px !important;\n              border: 2px solid #e8e8e8 !important;\n              border-radius: 16px !important;\n              font-size: 16px !important;\n              transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;\n              background: rgba(255, 255, 255, 0.9) !important;\n              backdrop-filter: blur(5px);\n              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;\n\n              &:focus {\n                border-color: #409EFF !important;\n                box-shadow: \n                  0 0 0 4px rgba(64, 158, 255, 0.15),\n                  0 4px 12px rgba(64, 158, 255, 0.1) !important;\n                background: rgba(255, 255, 255, 1) !important;\n                transform: translateY(-2px) !important;\n              }\n\n              &:hover {\n                border-color: #c0c4cc !important;\n                transform: translateY(-1px) !important;\n                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;\n              }\n            }\n          }\n\n          &:hover .input-icon {\n            color: #666;\n            transform: translateY(-50%) scale(1.1);\n          }\n        }\n\n        .custom-select {\n          width: 100%;\n\n          .el-input__inner {\n            height: 55px;\n            border: 2px solid #e8e8e8;\n            border-radius: 16px;\n            font-size: 16px;\n            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n            background: rgba(255, 255, 255, 0.9);\n            backdrop-filter: blur(5px);\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n\n            &:focus {\n              border-color: #409EFF;\n              box-shadow: \n                0 0 0 4px rgba(64, 158, 255, 0.15),\n                0 4px 12px rgba(64, 158, 255, 0.1);\n              background: rgba(255, 255, 255, 1);\n              transform: translateY(-2px);\n            }\n\n            &:hover {\n              border-color: #c0c4cc;\n              transform: translateY(-1px);\n              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n            }\n          }\n        }\n      }\n\n      .captcha-section {\n        margin-bottom: 30px;\n\n        .captcha-group {\n          display: flex;\n          align-items: center;\n          gap: 15px;\n\n          .captcha-input {\n            flex: 1;\n            margin-bottom: 0;\n          }\n\n          .captcha-image {\n            width: 120px;\n            height: 55px;\n            border-radius: 16px;\n            overflow: hidden;\n            cursor: pointer;\n            position: relative;\n            border: 2px solid #e8e8e8;\n            transition: all 0.3s ease;\n            background: rgba(255, 255, 255, 0.9);\n            backdrop-filter: blur(5px);\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n\n            &:hover {\n              border-color: #409EFF;\n              transform: scale(1.02);\n              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n            }\n\n            .captcha-img {\n              width: 100%;\n              height: 100%;\n              object-fit: cover;\n            }\n\n            .refresh-tip {\n              position: absolute;\n              bottom: 0;\n              left: 0;\n              right: 0;\n              background: rgba(0, 0, 0, 0.7);\n              color: #fff;\n              font-size: 12px;\n              text-align: center;\n              padding: 2px;\n              opacity: 0;\n              transition: opacity 0.3s ease;\n            }\n\n            &:hover .refresh-tip {\n              opacity: 1;\n            }\n          }\n        }\n      }\n\n      .register-btn-item {\n        margin-bottom: 25px;\n\n        .register-btn {\n          width: 100%;\n          height: 55px;\n          border-radius: 16px;\n          font-size: 17px;\n          font-weight: 700;\n          transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n          position: relative;\n          overflow: hidden;\n          border: none;\n          background: linear-gradient(135deg, #409EFF, #66b1ff);\n          box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);\n\n          &::before {\n            content: '';\n            position: absolute;\n            top: 0;\n            left: -100%;\n            width: 100%;\n            height: 100%;\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n            transition: left 0.5s ease;\n          }\n\n          &:hover {\n            transform: translateY(-3px) scale(1.02);\n            box-shadow: 0 12px 30px rgba(64, 158, 255, 0.4);\n            background: linear-gradient(135deg, #66b1ff, #409EFF);\n\n            &::before {\n              left: 100%;\n            }\n          }\n\n          &:active {\n            transform: translateY(-1px) scale(0.98);\n          }\n        }\n      }\n\n      .login-link {\n        text-align: center;\n        color: #666;\n        font-size: 14px;\n\n        .link {\n          color: #409EFF;\n          text-decoration: none;\n          font-weight: 500;\n          margin-left: 5px;\n          transition: color 0.3s ease;\n\n          &:hover {\n            color: #66b1ff;\n          }\n        }\n      }\n    }\n\n    // 主题样式\n    &.student-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #409EFF, #66b1ff);\n        color: #fff;\n      }\n    }\n\n    &.parent-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #67C23A, #85ce61);\n        color: #fff;\n      }\n    }\n\n    &.teacher-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #E6A23C, #f0c78a);\n        color: #fff;\n      }\n    }\n  }\n\n  .decoration {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    pointer-events: none;\n    z-index: 0;\n\n    .circle {\n      position: absolute;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      animation: float 6s ease-in-out infinite;\n\n      &.circle-1 {\n        width: 100px;\n        height: 100px;\n        top: 20%;\n        left: 10%;\n        animation-delay: 0s;\n      }\n\n      &.circle-2 {\n        width: 150px;\n        height: 150px;\n        top: 60%;\n        right: 10%;\n        animation-delay: 2s;\n      }\n\n      &.circle-3 {\n        width: 80px;\n        height: 80px;\n        bottom: 20%;\n        left: 20%;\n        animation-delay: 4s;\n      }\n    }\n  }\n}\n\n// 动画定义\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n@keyframes backgroundShift {\n  0%, 100% {\n    transform: translateX(0) translateY(0);\n  }\n  25% {\n    transform: translateX(-15px) translateY(-8px);\n  }\n  50% {\n    transform: translateX(15px) translateY(8px);\n  }\n  75% {\n    transform: translateX(-8px) translateY(15px);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n@keyframes shimmer {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .register {\n    padding: 10px;\n\n    .register-container {\n      max-width: 100%;\n    }\n\n    .register-card {\n      padding: 30px 20px;\n      margin-top: 40px;\n\n      .card-header .identity-icon {\n        width: 60px;\n        height: 60px;\n\n        .icon {\n          font-size: 2rem;\n        }\n      }\n\n      .identity-switch {\n        .switch-item {\n          padding: 10px 4px;\n          font-size: 12px;\n\n          .switch-icon {\n            font-size: 14px;\n          }\n        }\n      }\n\n      .register-form {\n        .form-section {\n          .form-row {\n            flex-direction: column;\n            gap: 0;\n\n            .form-item-half {\n              margin-bottom: 20px;\n            }\n          }\n        }\n      }\n    }\n\n    .back-btn {\n      top: -50px;\n    }\n  }\n}\n</style>\n"]}]}