package com.lingdu.system.mapper;

import java.util.List;
import com.lingdu.system.domain.UserParentStudent;

/**
 * 家长与学生绑定关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
public interface UserParentStudentMapper 
{
    /**
     * 查询家长与学生绑定关系
     * 
     * @param id 家长与学生绑定关系主键
     * @return 家长与学生绑定关系
     */
    public UserParentStudent selectUserParentStudentById(Long id);

    /**
     * 查询家长与学生绑定关系列表
     * 
     * @param userParentStudent 家长与学生绑定关系
     * @return 家长与学生绑定关系集合
     */
    public List<UserParentStudent> selectUserParentStudentList(UserParentStudent userParentStudent);

    /**
     * 新增家长与学生绑定关系
     * 
     * @param userParentStudent 家长与学生绑定关系
     * @return 结果
     */
    public int insertUserParentStudent(UserParentStudent userParentStudent);

    /**
     * 修改家长与学生绑定关系
     * 
     * @param userParentStudent 家长与学生绑定关系
     * @return 结果
     */
    public int updateUserParentStudent(UserParentStudent userParentStudent);

    /**
     * 删除家长与学生绑定关系
     * 
     * @param id 家长与学生绑定关系主键
     * @return 结果
     */
    public int deleteUserParentStudentById(Long id);

    /**
     * 批量删除家长与学生绑定关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserParentStudentByIds(Long[] ids);
}
