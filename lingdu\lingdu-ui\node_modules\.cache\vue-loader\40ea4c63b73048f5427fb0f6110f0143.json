{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\login.vue?vue&type=style&index=0&id=7589b93f&rel=stylesheet%2Fscss&lang=scss&scoped=true", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\login.vue", "mtime": 1758270592537}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758264042901}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758264044469}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758264043503}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758264042403}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758264043933}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAg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file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"login\">\n    <div class=\"login-container\">\n\n\n      <!-- 登录卡片 -->\n      <div class=\"login-card\" :class=\"`${loginForm.loginType}-theme`\">\n        <div class=\"card-header\">\n          <div class=\"identity-icon\">\n            <svg-icon :icon-class=\"getIdentityIcon()\" class=\"icon\" />\n          </div>\n          <h2 class=\"card-title\">{{ getIdentityTitle() }}</h2>\n          <p class=\"card-subtitle\">{{ getIdentitySubtitle() }}</p>\n        </div>\n\n        <el-form ref=\"loginForm\" :model=\"loginForm\" :rules=\"loginRules\" class=\"login-form\">\n          <!-- 身份切换 -->\n          <div class=\"identity-switch\">\n            <div\n              v-for=\"type in identityTypes\"\n              :key=\"type.value\"\n              class=\"switch-item\"\n              :class=\"{ active: loginForm.loginType === type.value }\"\n              @click=\"switchIdentity(type.value)\"\n            >\n              <svg-icon :icon-class=\"type.icon\" class=\"switch-icon\" />\n              <span>{{ type.label }}</span>\n            </div>\n          </div>\n\n          <el-form-item prop=\"username\">\n            <div class=\"input-group\">\n              <svg-icon icon-class=\"user\" class=\"input-icon\" />\n              <el-input\n                v-model=\"loginForm.username\"\n                type=\"text\"\n                auto-complete=\"off\"\n                placeholder=\"请输入账号\"\n                class=\"custom-input\"\n              />\n            </div>\n          </el-form-item>\n\n          <el-form-item prop=\"password\">\n            <div class=\"input-group\">\n              <svg-icon icon-class=\"password\" class=\"input-icon\" />\n              <el-input\n                v-model=\"loginForm.password\"\n                type=\"password\"\n                auto-complete=\"off\"\n                placeholder=\"请输入密码\"\n                class=\"custom-input\"\n                @keyup.enter.native=\"handleLogin\"\n              />\n            </div>\n          </el-form-item>\n\n          <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\n            <div class=\"captcha-group\">\n              <div class=\"input-group captcha-input\">\n                <svg-icon icon-class=\"validCode\" class=\"input-icon\" />\n                <el-input\n                  v-model=\"loginForm.code\"\n                  auto-complete=\"off\"\n                  placeholder=\"验证码\"\n                  class=\"custom-input\"\n                  @keyup.enter.native=\"handleLogin\"\n                />\n              </div>\n              <div class=\"captcha-image\" @click=\"getCode\">\n                <img :src=\"codeUrl\" class=\"captcha-img\"/>\n                <div class=\"refresh-tip\">点击刷新</div>\n              </div>\n            </div>\n          </el-form-item>\n\n          <div class=\"form-options\">\n            <el-checkbox v-model=\"loginForm.rememberMe\" class=\"remember-checkbox\">\n              记住密码\n            </el-checkbox>\n            <a href=\"#\" class=\"forgot-password\">忘记密码？</a>\n          </div>\n\n          <el-form-item class=\"login-btn-item\">\n            <el-button\n              :loading=\"loading\"\n              type=\"primary\"\n              class=\"login-btn\"\n              @click.native.prevent=\"handleLogin\"\n            >\n              <span v-if=\"!loading\">{{ getLoginButtonText() }}</span>\n              <span v-else>登录中...</span>\n            </el-button>\n          </el-form-item>\n\n          <div class=\"register-link\">\n            <span>还没有账号？</span>\n            <router-link to=\"/register\" class=\"link\">立即注册</router-link>\n          </div>\n        </el-form>\n      </div>\n\n      <!-- 装饰性元素 -->\n      <div class=\"decoration\">\n        <div class=\"circle circle-1\"></div>\n        <div class=\"circle circle-2\"></div>\n        <div class=\"circle circle-3\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg } from \"@/api/login\"\nimport Cookies from \"js-cookie\"\nimport { encrypt, decrypt } from '@/utils/jsencrypt'\n\nexport default {\n  name: \"Login\",\n  data() {\n    return {\n      title: process.env.VUE_APP_TITLE,\n      codeUrl: \"\",\n      loginForm: {\n        loginType: this.$route.query.type || \"student\",\n        username: \"\",\n        password: \"\",\n        rememberMe: false,\n        code: \"\",\n        uuid: \"\"\n      },\n      identityTypes: [\n        { value: \"student\", label: \"学生\", icon: \"education\" },\n        { value: \"parent\", label: \"家长\", icon: \"peoples\" },\n        { value: \"teacher\", label: \"教师\", icon: \"user\" }\n      ],\n      loginRules: {\n        loginType: [\n          { required: true, trigger: \"change\", message: \"请选择登录类型\" }\n        ],\n        username: [\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      // 验证码开关\n      captchaEnabled: true,\n      // 注册开关\n      register: true,\n      redirect: undefined\n    }\n  },\n  watch: {\n    $route: {\n      handler: function(route) {\n        this.redirect = route.query && route.query.redirect\n        // 更新登录类型\n        if (route.query && route.query.type) {\n          this.loginForm.loginType = route.query.type\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getCode()\n    this.getCookie()\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/gif;base64,\" + res.img\n          this.loginForm.uuid = res.uuid\n        }\n      })\n    },\n    switchIdentity(type) {\n      this.loginForm.loginType = type\n      this.$nextTick(() => {\n        this.$refs.loginForm.clearValidate()\n      })\n    },\n\n    getIdentityIcon() {\n      const type = this.identityTypes.find(t => t.value === this.loginForm.loginType)\n      return type ? type.icon : 'user'\n    },\n    getIdentityTitle() {\n      const type = this.identityTypes.find(t => t.value === this.loginForm.loginType)\n      return type ? `${type.label}登录` : '用户登录'\n    },\n    getIdentitySubtitle() {\n      const subtitles = {\n        student: '开始您的学习之旅',\n        parent: '关注孩子的学习成长',\n        teacher: '开启您的教学管理'\n      }\n      return subtitles[this.loginForm.loginType] || '欢迎回来'\n    },\n    getLoginButtonText() {\n      const texts = {\n        student: '开始学习',\n        parent: '进入管理',\n        teacher: '开始教学'\n      }\n      return texts[this.loginForm.loginType] || '登录'\n    },\n    getCookie() {\n      const username = Cookies.get(\"username\")\n      const password = Cookies.get(\"password\")\n      const rememberMe = Cookies.get('rememberMe')\n\n      // 只更新相关字段，保留其他字段不变\n      if (username !== undefined) {\n        this.loginForm.username = username\n      }\n      if (password !== undefined) {\n        this.loginForm.password = decrypt(password)\n      }\n      this.loginForm.rememberMe = rememberMe === undefined ? false : Boolean(rememberMe)\n    },\n    handleLogin() {\n      this.$refs.loginForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n          if (this.loginForm.rememberMe) {\n            Cookies.set(\"username\", this.loginForm.username, { expires: 30 })\n            Cookies.set(\"password\", encrypt(this.loginForm.password), { expires: 30 })\n            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 })\n          } else {\n            Cookies.remove(\"username\")\n            Cookies.remove(\"password\")\n            Cookies.remove('rememberMe')\n          }\n          this.$store.dispatch(\"Login\", this.loginForm).then(() => {\n            this.$router.push({ path: this.redirect || \"/\" }).catch(()=>{})\n          }).catch(() => {\n            this.loading = false\n            if (this.captchaEnabled) {\n              this.getCode()\n            }\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\n.login {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: url(\"../assets/images/login-background.jpg\") center/cover;\n    opacity: 0.1;\n    z-index: 0;\n  }\n\n  // 添加动态背景粒子效果\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-image:\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);\n    animation: backgroundShift 8s ease-in-out infinite;\n    z-index: 0;\n  }\n\n  .login-container {\n    position: relative;\n    z-index: 1;\n    width: 100%;\n    max-width: 450px;\n  }\n\n\n\n  .login-card {\n    background: rgba(255, 255, 255, 0.95);\n    border-radius: 24px;\n    padding: 45px;\n    box-shadow:\n      0 25px 50px rgba(0, 0, 0, 0.15),\n      0 10px 20px rgba(0, 0, 0, 0.1),\n      inset 0 1px 0 rgba(255, 255, 255, 0.6);\n    backdrop-filter: blur(15px);\n    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n    animation: slideInUp 0.8s ease-out;\n    position: relative;\n    overflow: hidden;\n    border: 1px solid rgba(255, 255, 255, 0.2);\n\n    // 光泽效果\n    &::before {\n      content: '';\n      position: absolute;\n      top: -50%;\n      left: -50%;\n      width: 200%;\n      height: 200%;\n      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);\n      transform: rotate(45deg);\n      transition: all 0.6s ease;\n      opacity: 0;\n      pointer-events: none;\n      z-index: 1;\n    }\n\n    // 边框光效\n    &::after {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      border-radius: 24px;\n      padding: 2px;\n      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n      mask-composite: exclude;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n      pointer-events: none;\n      z-index: 1;\n    }\n\n    &:hover {\n      transform: translateY(-8px) scale(1.02);\n      box-shadow:\n        0 35px 70px rgba(0, 0, 0, 0.25),\n        0 15px 30px rgba(0, 0, 0, 0.15),\n        inset 0 1px 0 rgba(255, 255, 255, 0.8);\n\n      &::before {\n        opacity: 1;\n        transform: rotate(45deg) translate(50%, 50%);\n      }\n\n      &::after {\n        opacity: 1;\n      }\n    }\n\n    .card-header {\n      text-align: center;\n      margin-bottom: 30px;\n\n      .identity-icon {\n        width: 80px;\n        height: 80px;\n        margin: 0 auto 20px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.3s ease;\n\n        .icon {\n          font-size: 2.5rem;\n        }\n      }\n\n      .card-title {\n        font-size: 1.8rem;\n        font-weight: 600;\n        margin: 0 0 10px 0;\n        color: #333;\n      }\n\n      .card-subtitle {\n        font-size: 1rem;\n        color: #666;\n        margin: 0;\n      }\n    }\n\n    .identity-switch {\n      display: flex;\n      background: rgba(245, 245, 245, 0.8);\n      border-radius: 16px;\n      padding: 6px;\n      margin-bottom: 35px;\n      backdrop-filter: blur(5px);\n      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);\n\n      .switch-item {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 14px 10px;\n        border-radius: 12px;\n        cursor: pointer;\n        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n        font-size: 15px;\n        font-weight: 600;\n        position: relative;\n        overflow: hidden;\n\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));\n          border-radius: 12px;\n          opacity: 0;\n          transition: opacity 0.3s ease;\n          pointer-events: none;\n        }\n\n        .switch-icon {\n          margin-right: 8px;\n          font-size: 18px;\n          transition: all 0.3s ease;\n        }\n\n        &.active {\n          background: #fff;\n          color: #409EFF;\n          box-shadow:\n            0 4px 12px rgba(0, 0, 0, 0.15),\n            0 2px 4px rgba(64, 158, 255, 0.2);\n          transform: translateY(-2px);\n\n          &::before {\n            opacity: 1;\n          }\n\n          .switch-icon {\n            transform: scale(1.1);\n          }\n        }\n\n        &:hover:not(.active) {\n          background: rgba(255, 255, 255, 0.9);\n          transform: translateY(-1px);\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n        }\n      }\n    }\n\n    .login-form {\n      position: relative;\n      z-index: 2;\n\n      .input-group {\n        position: relative;\n        margin-bottom: 25px;\n\n        .input-icon {\n          position: absolute;\n          left: 20px;\n          top: 50%;\n          transform: translateY(-50%);\n          color: #999;\n          font-size: 16px;\n          z-index: 3;\n          transition: all 0.3s ease;\n        }\n\n        .custom-input {\n          ::v-deep .el-input__inner {\n            height: 55px !important;\n            padding-left: 60px !important;\n            border: 2px solid #e8e8e8 !important;\n            border-radius: 16px !important;\n            font-size: 16px !important;\n            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;\n            background: rgba(255, 255, 255, 0.9) !important;\n            backdrop-filter: blur(5px);\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;\n\n            &:focus {\n              border-color: #409EFF !important;\n              box-shadow:\n                0 0 0 4px rgba(64, 158, 255, 0.15),\n                0 4px 12px rgba(64, 158, 255, 0.1) !important;\n              background: rgba(255, 255, 255, 1) !important;\n              transform: translateY(-2px) !important;\n            }\n\n            &:hover {\n              border-color: #c0c4cc !important;\n              transform: translateY(-1px) !important;\n              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;\n            }\n          }\n        }\n\n        &:hover .input-icon {\n          color: #666;\n          transform: translateY(-50%) scale(1.1);\n        }\n      }\n\n      .captcha-group {\n        display: flex;\n        gap: 15px;\n        margin-bottom: 20px;\n\n        .captcha-input {\n          flex: 1;\n        }\n\n        .captcha-image {\n          width: 120px;\n          height: 50px;\n          border-radius: 12px;\n          overflow: hidden;\n          cursor: pointer;\n          position: relative;\n          border: 2px solid #e8e8e8;\n          transition: all 0.3s ease;\n\n          &:hover {\n            border-color: #409EFF;\n            transform: scale(1.02);\n          }\n\n          .captcha-img {\n            width: 100%;\n            height: 100%;\n            object-fit: cover;\n          }\n\n          .refresh-tip {\n            position: absolute;\n            bottom: 0;\n            left: 0;\n            right: 0;\n            background: rgba(0, 0, 0, 0.7);\n            color: #fff;\n            font-size: 12px;\n            text-align: center;\n            padding: 2px;\n            opacity: 0;\n            transition: opacity 0.3s ease;\n          }\n\n          &:hover .refresh-tip {\n            opacity: 1;\n          }\n        }\n      }\n\n      .form-options {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 25px;\n\n        .remember-checkbox {\n          color: #666;\n        }\n\n        .forgot-password {\n          color: #409EFF;\n          text-decoration: none;\n          font-size: 14px;\n          transition: color 0.3s ease;\n\n          &:hover {\n            color: #66b1ff;\n          }\n        }\n      }\n\n      .login-btn-item {\n        margin-bottom: 25px;\n\n        .login-btn {\n          width: 100%;\n          height: 55px;\n          border-radius: 16px;\n          font-size: 17px;\n          font-weight: 700;\n          transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n          position: relative;\n          overflow: hidden;\n          border: none;\n          background: linear-gradient(135deg, #409EFF, #66b1ff);\n          box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);\n\n          &::before {\n            content: '';\n            position: absolute;\n            top: 0;\n            left: -100%;\n            width: 100%;\n            height: 100%;\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n            transition: left 0.5s ease;\n          }\n\n          &:hover {\n            transform: translateY(-3px) scale(1.02);\n            box-shadow: 0 12px 30px rgba(64, 158, 255, 0.4);\n            background: linear-gradient(135deg, #66b1ff, #409EFF);\n\n            &::before {\n              left: 100%;\n            }\n          }\n\n          &:active {\n            transform: translateY(-1px) scale(0.98);\n          }\n        }\n      }\n\n      .register-link {\n        text-align: center;\n        color: #666;\n        font-size: 14px;\n\n        .link {\n          color: #409EFF;\n          text-decoration: none;\n          font-weight: 500;\n          margin-left: 5px;\n          transition: color 0.3s ease;\n\n          &:hover {\n            color: #66b1ff;\n          }\n        }\n      }\n    }\n\n    // 主题样式\n    &.student-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #409EFF, #66b1ff);\n        color: #fff;\n      }\n    }\n\n    &.parent-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #67C23A, #85ce61);\n        color: #fff;\n      }\n    }\n\n    &.teacher-theme {\n      .card-header .identity-icon {\n        background: linear-gradient(135deg, #E6A23C, #f0c78a);\n        color: #fff;\n      }\n    }\n  }\n\n  .decoration {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    pointer-events: none;\n    z-index: 0;\n\n    .circle {\n      position: absolute;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      animation: float 6s ease-in-out infinite;\n\n      &.circle-1 {\n        width: 100px;\n        height: 100px;\n        top: 20%;\n        left: 10%;\n        animation-delay: 0s;\n      }\n\n      &.circle-2 {\n        width: 150px;\n        height: 150px;\n        top: 60%;\n        right: 10%;\n        animation-delay: 2s;\n      }\n\n      &.circle-3 {\n        width: 80px;\n        height: 80px;\n        bottom: 20%;\n        left: 20%;\n        animation-delay: 4s;\n      }\n    }\n  }\n}\n\n// 动画定义\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n@keyframes backgroundShift {\n  0%, 100% {\n    transform: translateX(0) translateY(0);\n  }\n  25% {\n    transform: translateX(-15px) translateY(-8px);\n  }\n  50% {\n    transform: translateX(15px) translateY(8px);\n  }\n  75% {\n    transform: translateX(-8px) translateY(15px);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n@keyframes shimmer {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .login {\n    padding: 10px;\n\n    .login-container {\n      max-width: 100%;\n    }\n\n    .login-card {\n      padding: 30px 20px;\n      margin-top: 40px;\n\n      .card-header .identity-icon {\n        width: 60px;\n        height: 60px;\n\n        .icon {\n          font-size: 2rem;\n        }\n      }\n\n      .identity-switch {\n        .switch-item {\n          padding: 10px 4px;\n          font-size: 12px;\n\n          .switch-icon {\n            font-size: 14px;\n          }\n        }\n      }\n    }\n\n\n  }\n}\n</style>\n"]}]}