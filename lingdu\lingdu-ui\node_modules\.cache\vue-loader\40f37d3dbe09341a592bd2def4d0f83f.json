{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\identity-select.vue?vue&type=style&index=0&id=2936a041&rel=stylesheet%2Fscss&lang=scss&scoped=true", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\identity-select.vue", "mtime": 1758268597206}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758264042901}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758264044469}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758264043503}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758264042403}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758264043933}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["identity-select.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm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file": "identity-select.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"identity-select\">\r\n    <div class=\"container\">\r\n      <div class=\"header\">\r\n        <h1 class=\"title\">{{ title }}</h1>\r\n        <p class=\"subtitle\">请选择您的身份类型</p>\r\n      </div>\r\n      \r\n      <div class=\"cards-container\">\r\n        <!-- 学生卡片 -->\r\n        <div class=\"identity-card student-card\" @click=\"selectIdentity('student')\">\r\n          <div class=\"card-icon\">\r\n            <svg-icon icon-class=\"education\" class=\"icon\" />\r\n          </div>\r\n          <div class=\"card-content\">\r\n            <h3 class=\"card-title\">学生</h3>\r\n            <p class=\"card-description\">学习课程、提交作业、查看学习进度</p>\r\n            <div class=\"card-features\">\r\n              <span class=\"feature-tag\">在线学习</span>\r\n              <span class=\"feature-tag\">作业提交</span>\r\n              <span class=\"feature-tag\">成绩查询</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"card-footer\">\r\n            <el-button type=\"primary\" class=\"action-btn\">开始学习</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 家长卡片 -->\r\n        <div class=\"identity-card parent-card\" @click=\"selectIdentity('parent')\">\r\n          <div class=\"card-icon\">\r\n            <svg-icon icon-class=\"peoples\" class=\"icon\" />\r\n          </div>\r\n          <div class=\"card-content\">\r\n            <h3 class=\"card-title\">家长</h3>\r\n            <p class=\"card-description\">管理孩子学习、监控学习进度、接收通知</p>\r\n            <div class=\"card-features\">\r\n              <span class=\"feature-tag\">学习监控</span>\r\n              <span class=\"feature-tag\">进度跟踪</span>\r\n              <span class=\"feature-tag\">消息通知</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"card-footer\">\r\n            <el-button type=\"success\" class=\"action-btn\">管理孩子</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 教师卡片 -->\r\n        <div class=\"identity-card teacher-card\" @click=\"selectIdentity('teacher')\">\r\n          <div class=\"card-icon\">\r\n            <svg-icon icon-class=\"user\" class=\"icon\" />\r\n          </div>\r\n          <div class=\"card-content\">\r\n            <h3 class=\"card-title\">教师</h3>\r\n            <p class=\"card-description\">管理学生、布置作业、批改作业、查看成绩</p>\r\n            <div class=\"card-features\">\r\n              <span class=\"feature-tag\">学生管理</span>\r\n              <span class=\"feature-tag\">作业批改</span>\r\n              <span class=\"feature-tag\">成绩统计</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"card-footer\">\r\n            <el-button type=\"warning\" class=\"action-btn\">开始教学</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"footer-actions\">\r\n        <p class=\"login-tip\">已有账号？</p>\r\n        <el-button type=\"text\" class=\"login-link\" @click=\"goToLogin\">直接登录</el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"IdentitySelect\",\r\n  data() {\r\n    return {\r\n      title: process.env.VUE_APP_TITLE\r\n    }\r\n  },\r\n  methods: {\r\n    selectIdentity(identity) {\r\n      // 跳转到对应身份的注册页面\r\n      this.$router.push({\r\n        path: '/register',\r\n        query: { type: identity }\r\n      })\r\n    },\r\n    goToLogin() {\r\n      this.$router.push('/login')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\r\n.identity-select {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px;\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: url('../assets/images/login-background.jpg') center/cover;\r\n    opacity: 0.1;\r\n    z-index: 0;\r\n  }\r\n\r\n  // 添加动态背景粒子效果\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-image: \r\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\r\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\r\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);\r\n    animation: backgroundShift 10s ease-in-out infinite;\r\n    z-index: 0;\r\n  }\r\n\r\n  .container {\r\n    position: relative;\r\n    z-index: 1;\r\n    max-width: 1200px;\r\n    width: 100%;\r\n  }\r\n\r\n  .header {\r\n    text-align: center;\r\n    margin-bottom: 60px;\r\n    \r\n    .title {\r\n      font-size: 3rem;\r\n      font-weight: 700;\r\n      color: #fff;\r\n      margin: 0 0 20px 0;\r\n      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n      animation: fadeInDown 1s ease-out;\r\n      background: linear-gradient(45deg, #fff, #f0f8ff);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      background-clip: text;\r\n    }\r\n    \r\n    .subtitle {\r\n      font-size: 1.2rem;\r\n      color: rgba(255, 255, 255, 0.9);\r\n      margin: 0;\r\n      animation: fadeInUp 1s ease-out 0.3s both;\r\n    }\r\n  }\r\n\r\n  .cards-container {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\r\n    gap: 40px;\r\n    margin-bottom: 40px;\r\n    perspective: 1000px;\r\n  }\r\n\r\n  .identity-card {\r\n    background: rgba(255, 255, 255, 0.95);\r\n    border-radius: 24px;\r\n    padding: 45px 35px;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n    box-shadow: \r\n      0 15px 35px rgba(0, 0, 0, 0.1),\r\n      0 5px 15px rgba(0, 0, 0, 0.08);\r\n    position: relative;\r\n    overflow: hidden;\r\n    animation: fadeInUp 1s ease-out;\r\n    backdrop-filter: blur(10px);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n\r\n    &:nth-child(1) { animation-delay: 0.1s; }\r\n    &:nth-child(2) { animation-delay: 0.2s; }\r\n    &:nth-child(3) { animation-delay: 0.3s; }\r\n\r\n    // 光泽效果\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: -100%;\r\n      width: 100%;\r\n      height: 100%;\r\n      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\r\n      transition: left 0.6s ease;\r\n    }\r\n\r\n    // 边框光效\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      border-radius: 24px;\r\n      padding: 2px;\r\n      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\r\n      mask-composite: exclude;\r\n      opacity: 0;\r\n      transition: opacity 0.3s ease;\r\n    }\r\n\r\n    &:hover {\r\n      transform: translateY(-20px) rotateX(5deg) rotateY(5deg) scale(1.05);\r\n      box-shadow: \r\n        0 30px 60px rgba(0, 0, 0, 0.25),\r\n        0 15px 30px rgba(0, 0, 0, 0.15),\r\n        inset 0 1px 0 rgba(255, 255, 255, 0.6);\r\n\r\n      &::before {\r\n        left: 100%;\r\n      }\r\n\r\n      &::after {\r\n        opacity: 1;\r\n      }\r\n\r\n      .card-icon {\r\n        transform: translateY(-10px);\r\n        \r\n        .icon {\r\n          transform: scale(1.2) rotate(10deg);\r\n        }\r\n      }\r\n\r\n      .card-content {\r\n        .card-title {\r\n          transform: translateY(-5px);\r\n        }\r\n\r\n        .feature-tag {\r\n          transform: translateY(-3px);\r\n          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n        }\r\n      }\r\n\r\n      .action-btn {\r\n        transform: translateY(-5px) scale(1.05);\r\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\r\n      }\r\n    }\r\n\r\n    .card-icon {\r\n      margin-bottom: 30px;\r\n      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n      \r\n      .icon {\r\n        font-size: 4.5rem;\r\n        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));\r\n      }\r\n    }\r\n\r\n    .card-content {\r\n      margin-bottom: 35px;\r\n\r\n      .card-title {\r\n        font-size: 2rem;\r\n        font-weight: 700;\r\n        margin: 0 0 15px 0;\r\n        color: #333;\r\n        transition: all 0.3s ease;\r\n        background: linear-gradient(45deg, #333, #666);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n      }\r\n\r\n      .card-description {\r\n        font-size: 1.1rem;\r\n        color: #666;\r\n        line-height: 1.7;\r\n        margin: 0 0 25px 0;\r\n        font-weight: 400;\r\n      }\r\n\r\n      .card-features {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        justify-content: center;\r\n        gap: 12px;\r\n\r\n        .feature-tag {\r\n          background: #f8f9fa;\r\n          color: #495057;\r\n          padding: 8px 16px;\r\n          border-radius: 25px;\r\n          font-size: 0.9rem;\r\n          font-weight: 500;\r\n          transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n          border: 1px solid rgba(0, 0, 0, 0.05);\r\n          backdrop-filter: blur(5px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .card-footer {\r\n      .action-btn {\r\n        padding: 15px 35px;\r\n        border-radius: 30px;\r\n        font-weight: 700;\r\n        font-size: 1.1rem;\r\n        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n        border: none;\r\n        min-width: 160px;\r\n        position: relative;\r\n        overflow: hidden;\r\n\r\n        &::before {\r\n          content: '';\r\n          position: absolute;\r\n          top: 0;\r\n          left: -100%;\r\n          width: 100%;\r\n          height: 100%;\r\n          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n          transition: left 0.5s ease;\r\n        }\r\n\r\n        &:hover::before {\r\n          left: 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 不同身份卡片的特殊样式\r\n    &.student-card {\r\n      .card-icon .icon {\r\n        color: #409EFF;\r\n        background: linear-gradient(135deg, #409EFF, #66b1ff);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n      }\r\n      \r\n      .feature-tag {\r\n        background: linear-gradient(135deg, #e6f7ff, #f0f9ff);\r\n        color: #1890ff;\r\n        border-color: rgba(24, 144, 255, 0.2);\r\n      }\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(230, 247, 255, 0.95));\r\n      }\r\n    }\r\n\r\n    &.parent-card {\r\n      .card-icon .icon {\r\n        color: #67C23A;\r\n        background: linear-gradient(135deg, #67C23A, #85ce61);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n      }\r\n      \r\n      .feature-tag {\r\n        background: linear-gradient(135deg, #f6ffed, #f9fff6);\r\n        color: #52c41a;\r\n        border-color: rgba(82, 196, 26, 0.2);\r\n      }\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(246, 255, 237, 0.95));\r\n      }\r\n    }\r\n\r\n    &.teacher-card {\r\n      .card-icon .icon {\r\n        color: #E6A23C;\r\n        background: linear-gradient(135deg, #E6A23C, #f0c78a);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n        background-clip: text;\r\n      }\r\n      \r\n      .feature-tag {\r\n        background: linear-gradient(135deg, #fffbe6, #fffef0);\r\n        color: #faad14;\r\n        border-color: rgba(250, 173, 20, 0.2);\r\n      }\r\n\r\n      &:hover {\r\n        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(255, 251, 230, 0.95));\r\n      }\r\n    }\r\n  }\r\n\r\n  .footer-actions {\r\n    text-align: center;\r\n    animation: fadeInUp 1s ease-out 0.6s both;\r\n\r\n    .login-tip {\r\n      color: rgba(255, 255, 255, 0.8);\r\n      margin: 0 10px 0 0;\r\n      display: inline-block;\r\n      font-size: 1.1rem;\r\n    }\r\n\r\n    .login-link {\r\n      color: #fff;\r\n      font-size: 1.2rem;\r\n      font-weight: 600;\r\n      text-decoration: none;\r\n      transition: all 0.3s ease;\r\n      padding: 8px 16px;\r\n      border-radius: 20px;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      backdrop-filter: blur(5px);\r\n\r\n      &:hover {\r\n        color: #ffd700;\r\n        text-shadow: 0 0 15px rgba(255, 215, 0, 0.6);\r\n        background: rgba(255, 255, 255, 0.2);\r\n        transform: translateY(-2px);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 动画定义\r\n@keyframes fadeInDown {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes backgroundShift {\r\n  0%, 100% {\r\n    transform: translateX(0) translateY(0);\r\n  }\r\n  25% {\r\n    transform: translateX(-20px) translateY(-10px);\r\n  }\r\n  50% {\r\n    transform: translateX(20px) translateY(10px);\r\n  }\r\n  75% {\r\n    transform: translateX(-10px) translateY(20px);\r\n  }\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-10px);\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.05);\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 1200px) {\r\n  .identity-select {\r\n    .cards-container {\r\n      gap: 30px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .identity-select {\r\n    padding: 15px;\r\n\r\n    .header {\r\n      margin-bottom: 40px;\r\n      \r\n      .title {\r\n        font-size: 2.2rem;\r\n      }\r\n      \r\n      .subtitle {\r\n        font-size: 1.1rem;\r\n      }\r\n    }\r\n    \r\n    .cards-container {\r\n      grid-template-columns: 1fr;\r\n      gap: 25px;\r\n    }\r\n    \r\n    .identity-card {\r\n      padding: 35px 25px;\r\n      border-radius: 20px;\r\n\r\n      &:hover {\r\n        transform: translateY(-15px) scale(1.02);\r\n      }\r\n\r\n      .card-icon .icon {\r\n        font-size: 3.5rem;\r\n      }\r\n\r\n      .card-content {\r\n        .card-title {\r\n          font-size: 1.6rem;\r\n        }\r\n\r\n        .card-description {\r\n          font-size: 1rem;\r\n        }\r\n\r\n        .card-features {\r\n          gap: 8px;\r\n\r\n          .feature-tag {\r\n            font-size: 0.85rem;\r\n            padding: 6px 12px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .card-footer .action-btn {\r\n        padding: 12px 28px;\r\n        font-size: 1rem;\r\n        min-width: 140px;\r\n      }\r\n    }\r\n\r\n    .footer-actions {\r\n      .login-tip {\r\n        font-size: 1rem;\r\n      }\r\n\r\n      .login-link {\r\n        font-size: 1.1rem;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .identity-select {\r\n    padding: 10px;\r\n\r\n    .header {\r\n      margin-bottom: 30px;\r\n      \r\n      .title {\r\n        font-size: 1.8rem;\r\n      }\r\n      \r\n      .subtitle {\r\n        font-size: 1rem;\r\n      }\r\n    }\r\n    \r\n    .cards-container {\r\n      gap: 20px;\r\n    }\r\n    \r\n    .identity-card {\r\n      padding: 30px 20px;\r\n\r\n      .card-icon .icon {\r\n        font-size: 3rem;\r\n      }\r\n\r\n      .card-content {\r\n        margin-bottom: 25px;\r\n\r\n        .card-title {\r\n          font-size: 1.4rem;\r\n        }\r\n\r\n        .card-description {\r\n          font-size: 0.95rem;\r\n        }\r\n\r\n        .card-features {\r\n          .feature-tag {\r\n            font-size: 0.8rem;\r\n            padding: 5px 10px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .card-footer .action-btn {\r\n        padding: 10px 24px;\r\n        font-size: 0.95rem;\r\n        min-width: 120px;\r\n      }\r\n    }\r\n\r\n    .footer-actions {\r\n      .login-tip, .login-link {\r\n        font-size: 0.95rem;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}