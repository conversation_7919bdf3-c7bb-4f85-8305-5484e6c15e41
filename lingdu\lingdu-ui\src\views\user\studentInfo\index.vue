<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select v-model="queryParams.grade" placeholder="请选择年级" clearable>
          <el-option
            v-for="dict in dict.type.student_grade"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学段" prop="stage">
        <el-select v-model="queryParams.stage" placeholder="请选择学段" clearable>
          <el-option
            v-for="dict in dict.type.student_stage"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="主学科" prop="mainSubject">
        <el-select v-model="queryParams.mainSubject" placeholder="请选择主学科" clearable>
          <el-option
            v-for="dict in dict.type.course_subject"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="会员类型" prop="membershipType">
        <el-select v-model="queryParams.membershipType" placeholder="请选择会员类型" clearable>
          <el-option
            v-for="dict in dict.type.membership_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['user:studentInfo:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['user:studentInfo:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['user:studentInfo:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['user:studentInfo:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="studentInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="学生ID" align="center" prop="studentId" />
      <el-table-column label="昵称" align="center" prop="nickName" />
      <el-table-column label="年级" align="center" prop="grade">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.student_grade" :value="scope.row.grade"/>
        </template>
      </el-table-column>
      <el-table-column label="学段" align="center" prop="stage">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.student_stage" :value="scope.row.stage"/>
        </template>
      </el-table-column>
      <el-table-column label="主学科" align="center" prop="mainSubject">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.course_subject" :value="scope.row.mainSubject"/>
        </template>
      </el-table-column>
      <el-table-column label="会员类型" align="center" prop="membershipType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.membership_type" :value="scope.row.membershipType"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['user:studentInfo:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['user:studentInfo:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改学生信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户昵称" prop="nickName">
          <el-select v-model="form.nickName" placeholder="请选择用户" clearable filterable>
            <el-option
              v-for="user in userList"
              :key="user.userName"
              :label="user.nickName"
              :value="user.nickName">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年级" prop="grade">
          <el-select v-model="form.grade" placeholder="请选择年级">
            <el-option
              v-for="dict in dict.type.student_grade"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学段" prop="stage">
          <el-select v-model="form.stage" placeholder="请选择学段">
            <el-option
              v-for="dict in dict.type.student_stage"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主学科" prop="mainSubject">
          <el-select v-model="form.mainSubject" placeholder="请选择主学科">
            <el-option
              v-for="dict in dict.type.course_subject"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会员类型" prop="membershipType">
          <el-select v-model="form.membershipType" placeholder="请选择会员类型">
            <el-option
              v-for="dict in dict.type.membership_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStudentInfo, getStudentInfo, delStudentInfo, addStudentInfo, updateStudentInfo } from "@/api/user/studentInfo"
import { listUser } from "@/api/system/user"

export default {
  name: "StudentInfo",
  dicts: ['membership_type', 'course_subject', 'student_grade', 'student_stage'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学生信息表格数据
      studentInfoList: [],
      // 用户列表
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nickName: null,
        grade: null,
        stage: null,
        mainSubject: null,
        membershipType: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        nickName: [
          { required: true, message: "用户昵称不能为空", trigger: "change" }
        ],
        grade: [
          { required: true, message: "年级不能为空", trigger: "change" }
        ],
        stage: [
          { required: true, message: "学段不能为空", trigger: "change" }
        ],
        mainSubject: [
          { required: true, message: "主学科不能为空", trigger: "change" }
        ],
        membershipType: [
          { required: true, message: "会员类型不能为空", trigger: "change" }
        ],
      }
    }
  },
  created() {
    this.getList()
    this.getUserList()
  },
  methods: {
    /** 查询学生信息列表 */
    getList() {
      this.loading = true
      listStudentInfo(this.queryParams).then(response => {
        // 对返回的数据进行去重处理，确保每个 studentId 只出现一次
        const uniqueData = [...new Map(response.rows.map(item => [item.studentId, item])).values()]
        this.studentInfoList = uniqueData
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询用户列表 */
    getUserList() {
      listUser({ pageNum: 1, pageSize: 1000, status: '0' }).then(response => {
        this.userList = response.rows || []
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        studentId: null,
        nickName: null,
        grade: null,
        stage: null,
        mainSubject: null,
        extraSubjects: null,
        membershipType: null,
        createTime: null,
        updateTime: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.studentId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加学生信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const studentId = row.studentId || this.ids
      getStudentInfo(studentId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改学生信息"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.studentId != null) {
            updateStudentInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addStudentInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const studentIds = row.studentId || this.ids
      this.$modal.confirm('是否确认删除学生信息编号为"' + studentIds + '"的数据项？').then(function() {
        return delStudentInfo(studentIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('user/studentInfo/export', {
        ...this.queryParams
      }, `studentInfo_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
