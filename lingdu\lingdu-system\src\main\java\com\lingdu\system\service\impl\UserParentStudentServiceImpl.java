package com.lingdu.system.service.impl;

import java.util.List;
import com.lingdu.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lingdu.system.mapper.UserParentStudentMapper;
import com.lingdu.system.domain.UserParentStudent;
import com.lingdu.system.service.IUserParentStudentService;

/**
 * 家长与学生绑定关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
@Service
public class UserParentStudentServiceImpl implements IUserParentStudentService 
{
    @Autowired
    private UserParentStudentMapper userParentStudentMapper;

    /**
     * 查询家长与学生绑定关系
     * 
     * @param id 家长与学生绑定关系主键
     * @return 家长与学生绑定关系
     */
    @Override
    public UserParentStudent selectUserParentStudentById(Long id)
    {
        return userParentStudentMapper.selectUserParentStudentById(id);
    }

    /**
     * 查询家长与学生绑定关系列表
     * 
     * @param userParentStudent 家长与学生绑定关系
     * @return 家长与学生绑定关系
     */
    @Override
    public List<UserParentStudent> selectUserParentStudentList(UserParentStudent userParentStudent)
    {
        return userParentStudentMapper.selectUserParentStudentList(userParentStudent);
    }

    /**
     * 新增家长与学生绑定关系
     * 
     * @param userParentStudent 家长与学生绑定关系
     * @return 结果
     */
    @Override
    public int insertUserParentStudent(UserParentStudent userParentStudent)
    {
        userParentStudent.setCreateTime(DateUtils.getNowDate());
        return userParentStudentMapper.insertUserParentStudent(userParentStudent);
    }

    /**
     * 修改家长与学生绑定关系
     * 
     * @param userParentStudent 家长与学生绑定关系
     * @return 结果
     */
    @Override
    public int updateUserParentStudent(UserParentStudent userParentStudent)
    {
        return userParentStudentMapper.updateUserParentStudent(userParentStudent);
    }

    /**
     * 批量删除家长与学生绑定关系
     * 
     * @param ids 需要删除的家长与学生绑定关系主键
     * @return 结果
     */
    @Override
    public int deleteUserParentStudentByIds(Long[] ids)
    {
        return userParentStudentMapper.deleteUserParentStudentByIds(ids);
    }

    /**
     * 删除家长与学生绑定关系信息
     * 
     * @param id 家长与学生绑定关系主键
     * @return 结果
     */
    @Override
    public int deleteUserParentStudentById(Long id)
    {
        return userParentStudentMapper.deleteUserParentStudentById(id);
    }
}
