{"remainingRequest": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\identity-select.vue?vue&type=template&id=2936a041&scoped=true", "dependencies": [{"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\src\\views\\identity-select.vue", "mtime": 1758268597206}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758264044512}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758264042402}, {"path": "E:\\study\\ruoyi\\lingdu\\lingdu\\lingdu-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758264043933}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}