package com.lingdu.system.mapper;

import java.util.List;
import com.lingdu.system.domain.TeacherInfo;

/**
 * 教师信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-17
 */
public interface TeacherInfoMapper 
{
    /**
     * 查询教师信息
     * 
     * @param teacherId 教师信息主键
     * @return 教师信息
     */
    public TeacherInfo selectTeacherInfoByTeacherId(Long teacherId);

    /**
     * 查询教师信息列表
     * 
     * @param teacherInfo 教师信息
     * @return 教师信息集合
     */
    public List<TeacherInfo> selectTeacherInfoList(TeacherInfo teacherInfo);

    /**
     * 新增教师信息
     * 
     * @param teacherInfo 教师信息
     * @return 结果
     */
    public int insertTeacherInfo(TeacherInfo teacherInfo);

    /**
     * 修改教师信息
     * 
     * @param teacherInfo 教师信息
     * @return 结果
     */
    public int updateTeacherInfo(TeacherInfo teacherInfo);

    /**
     * 删除教师信息
     * 
     * @param teacherId 教师信息主键
     * @return 结果
     */
    public int deleteTeacherInfoByTeacherId(Long teacherId);

    /**
     * 批量删除教师信息
     * 
     * @param teacherIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTeacherInfoByTeacherIds(Long[] teacherIds);
}
