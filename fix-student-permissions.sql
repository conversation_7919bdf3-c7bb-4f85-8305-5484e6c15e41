-- 修复学生权限问题的完整SQL脚本

-- 1. 修复已注册用户的角色分配问题
-- 删除错误的角色分配
DELETE FROM sys_user_role WHERE user_id = 102 AND role_id = 3;

-- 为用户ID 100（李木子）分配学生角色
INSERT IGNORE INTO sys_user_role VALUES(100, 100);

-- 为用户ID 102（cs）分配学生角色（假设cs是学生）
INSERT IGNORE INTO sys_user_role VALUES(102, 100);

-- 2. 清除现有的学生、家长、教师角色菜单关联
DELETE FROM sys_role_menu WHERE role_id IN (100, 101, 102);

-- 3. 为学生角色(ID=100)分配基础菜单权限
-- 系统管理相关
INSERT INTO sys_role_menu VALUES(100, 1);    -- 系统管理
INSERT INTO sys_role_menu VALUES(100, 100);  -- 用户管理
INSERT INTO sys_role_menu VALUES(100, 103);  -- 部门管理
INSERT INTO sys_role_menu VALUES(100, 104);  -- 岗位管理

-- 系统监控相关
INSERT INTO sys_role_menu VALUES(100, 2);    -- 系统监控
INSERT INTO sys_role_menu VALUES(100, 500);  -- 操作日志
INSERT INTO sys_role_menu VALUES(100, 501);  -- 登录日志

-- 个人中心相关功能
INSERT INTO sys_role_menu VALUES(100, 1001); -- 用户查询
INSERT INTO sys_role_menu VALUES(100, 1040); -- 操作查询
INSERT INTO sys_role_menu VALUES(100, 1042); -- 登录查询

-- 4. 为家长角色(ID=101)分配菜单权限
INSERT INTO sys_role_menu VALUES(101, 1);    -- 系统管理
INSERT INTO sys_role_menu VALUES(101, 100);  -- 用户管理
INSERT INTO sys_role_menu VALUES(101, 103);  -- 部门管理
INSERT INTO sys_role_menu VALUES(101, 2);    -- 系统监控
INSERT INTO sys_role_menu VALUES(101, 500);  -- 操作日志
INSERT INTO sys_role_menu VALUES(101, 501);  -- 登录日志
INSERT INTO sys_role_menu VALUES(101, 1001); -- 用户查询
INSERT INTO sys_role_menu VALUES(101, 1040); -- 操作查询
INSERT INTO sys_role_menu VALUES(101, 1042); -- 登录查询

-- 5. 为教师角色(ID=102)分配菜单权限
INSERT INTO sys_role_menu VALUES(102, 1);    -- 系统管理
INSERT INTO sys_role_menu VALUES(102, 100);  -- 用户管理
INSERT INTO sys_role_menu VALUES(102, 101);  -- 角色管理
INSERT INTO sys_role_menu VALUES(102, 103);  -- 部门管理
INSERT INTO sys_role_menu VALUES(102, 104);  -- 岗位管理
INSERT INTO sys_role_menu VALUES(102, 2);    -- 系统监控
INSERT INTO sys_role_menu VALUES(102, 500);  -- 操作日志
INSERT INTO sys_role_menu VALUES(102, 501);  -- 登录日志
INSERT INTO sys_role_menu VALUES(102, 1001); -- 用户查询
INSERT INTO sys_role_menu VALUES(102, 1002); -- 用户新增
INSERT INTO sys_role_menu VALUES(102, 1040); -- 操作查询
INSERT INTO sys_role_menu VALUES(102, 1042); -- 登录查询

-- 6. 验证修复结果
-- 检查用户角色分配
SELECT '=== 用户角色分配情况 ===' as info;
SELECT u.user_id, u.user_name, u.nick_name, ur.role_id, r.role_name
FROM sys_user u
LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.user_name != 'admin' AND u.user_name != 'ry'
ORDER BY u.user_id;

-- 检查角色菜单权限数量
SELECT '=== 角色菜单权限数量 ===' as info;
SELECT rm.role_id, r.role_name, COUNT(rm.menu_id) as menu_count
FROM sys_role_menu rm
LEFT JOIN sys_role r ON rm.role_id = r.role_id
WHERE rm.role_id IN (100, 101, 102)
GROUP BY rm.role_id, r.role_name
ORDER BY rm.role_id;

-- 检查学生角色的具体菜单权限
SELECT '=== 学生角色菜单权限详情 ===' as info;
SELECT rm.role_id, r.role_name, rm.menu_id, m.menu_name, m.path
FROM sys_role_menu rm
LEFT JOIN sys_role r ON rm.role_id = r.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE rm.role_id = 100
ORDER BY rm.menu_id;
