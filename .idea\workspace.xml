<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="60d85af8-f7fc-40d2-b348-a19d0c1302bc" name="更改" comment="修复家长信息字段不统一">
      <change beforePath="$PROJECT_DIR$/lingdu/lingdu-common/src/main/java/com/lingdu/common/core/domain/model/LoginBody.java" beforeDir="false" afterPath="$PROJECT_DIR$/lingdu/lingdu-common/src/main/java/com/lingdu/common/core/domain/model/LoginBody.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lingdu/lingdu-common/src/main/java/com/lingdu/common/core/domain/model/RegisterBody.java" beforeDir="false" afterPath="$PROJECT_DIR$/lingdu/lingdu-common/src/main/java/com/lingdu/common/core/domain/model/RegisterBody.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lingdu/lingdu-framework/src/main/java/com/lingdu/framework/web/service/SysLoginService.java" beforeDir="false" afterPath="$PROJECT_DIR$/lingdu/lingdu-framework/src/main/java/com/lingdu/framework/web/service/SysLoginService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lingdu/lingdu-framework/src/main/java/com/lingdu/framework/web/service/SysRegisterService.java" beforeDir="false" afterPath="$PROJECT_DIR$/lingdu/lingdu-framework/src/main/java/com/lingdu/framework/web/service/SysRegisterService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lingdu/lingdu-ui/src/api/login.js" beforeDir="false" afterPath="$PROJECT_DIR$/lingdu/lingdu-ui/src/api/login.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lingdu/lingdu-ui/src/permission.js" beforeDir="false" afterPath="$PROJECT_DIR$/lingdu/lingdu-ui/src/permission.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lingdu/lingdu-ui/src/router/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/lingdu/lingdu-ui/src/router/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lingdu/lingdu-ui/src/views/login.vue" beforeDir="false" afterPath="$PROJECT_DIR$/lingdu/lingdu-ui/src/views/login.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lingdu/lingdu-ui/src/views/register.vue" beforeDir="false" afterPath="$PROJECT_DIR$/lingdu/lingdu-ui/src/views/register.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="32uHHrqhLbe9Pf4aZO7tS1g5osH" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.RuoYiApplication.executor": "Run",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "ts.external.directory.path": "C:\\Users\\<USER>\\AppData\\Roaming\\JetBrains\\IntelliJIdea2025.2\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="RuoYiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="lingdu-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lingdu.RuoYiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-bf35d07a577b-intellij.indexing.shared.core-IU-252.25557.131" />
        <option value="bundled-js-predefined-d6986cc7102b-b598e85cdad2-JavaScript-IU-252.25557.131" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="60d85af8-f7fc-40d2-b348-a19d0c1302bc" name="更改" comment="" />
      <created>1758263456127</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1758263456127</updated>
      <workItem from="1758263457816" duration="314000" />
      <workItem from="1758263811219" duration="5589000" />
    </task>
    <task id="LOCAL-00001" summary="修复家长信息字段不统一">
      <option name="closed" value="true" />
      <created>1758265133965</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1758265133965</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="修复家长信息字段不统一" />
    <option name="LAST_COMMIT_MESSAGE" value="修复家长信息字段不统一" />
  </component>
</project>